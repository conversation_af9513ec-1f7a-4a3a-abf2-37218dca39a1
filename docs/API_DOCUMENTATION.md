# CryptoForensics v3.0 API Documentation

## Overview

The CryptoForensics v3.0 API provides comprehensive access to victim-centric investigation management features, evidence collection, communication systems, and professional reporting capabilities.

## Base URL

```
https://api.cryptoforensics.com/v3
```

## Authentication

All API requests require authentication using Bearer tokens:

```http
Authorization: Bearer <your-api-token>
```

## Rate Limiting

- **Standard**: 1000 requests per hour
- **Premium**: 5000 requests per hour
- **Enterprise**: Unlimited

## Victim Management API

### Create Victim Profile

Create a new victim profile for investigation management.

```http
POST /victims/profiles
```

**Request Body:**
```json
{
  "full_name": "<PERSON>",
  "email": "<EMAIL>",
  "phone": "******-0123",
  "incident_description": "Cryptocurrency theft from wallet",
  "estimated_loss": 2.5,
  "currency": "BTC",
  "affected_addresses": ["**********************************"],
  "gdpr_consent": true,
  "preferred_contact_method": "email"
}
```

**Response:**
```json
{
  "victim_id": "VIC_20240115_001",
  "full_name": "John Doe",
  "email": "<EMAIL>",
  "communication_status": "initial_contact",
  "created_at": "2024-01-15T10:00:00Z",
  "folder_path": "/investigations/VIC_20240115_001"
}
```

### Get Victim Profile

Retrieve victim profile information.

```http
GET /victims/profiles/{victim_id}
```

**Response:**
```json
{
  "victim_id": "VIC_20240115_001",
  "full_name": "John Doe",
  "email": "<EMAIL>",
  "phone": "******-0123",
  "incident_description": "Cryptocurrency theft from wallet",
  "estimated_loss": 2.5,
  "currency": "BTC",
  "affected_addresses": ["**********************************"],
  "communication_status": "active_investigation",
  "data_protection_level": "high",
  "created_at": "2024-01-15T10:00:00Z",
  "updated_at": "2024-01-15T12:00:00Z"
}
```

### Update Victim Profile

Update victim profile information.

```http
PUT /victims/profiles/{victim_id}
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "phone": "******-9999",
  "preferred_contact_method": "phone"
}
```

### List Victim Profiles

List all victim profiles with filtering options.

```http
GET /victims/profiles?status=active&limit=50&offset=0
```

**Query Parameters:**
- `status`: Filter by communication status
- `currency`: Filter by affected currency
- `created_after`: Filter by creation date
- `limit`: Number of results (default: 50, max: 100)
- `offset`: Pagination offset

## Investigation Case Management API

### Create Investigation Case

Create a new investigation case for a victim.

```http
POST /victims/{victim_id}/cases
```

**Request Body:**
```json
{
  "case_title": "Bitcoin Theft Investigation",
  "case_description": "Investigation of stolen Bitcoin from victim wallet",
  "case_priority": "high",
  "target_addresses": ["**********************************"],
  "investigation_depth": 5,
  "assigned_investigator": "investigator_001"
}
```

**Response:**
```json
{
  "case_id": "CASE_20240115_001",
  "victim_id": "VIC_20240115_001",
  "case_title": "Bitcoin Theft Investigation",
  "case_status": "intake",
  "progress_percentage": 10,
  "case_folder_path": "/investigations/VIC_20240115_001/CASE_20240115_001",
  "created_at": "2024-01-15T10:30:00Z"
}
```

### Update Case Status

Update investigation case status and progress.

```http
PUT /cases/{case_id}/status
```

**Request Body:**
```json
{
  "status": "active",
  "status_notes": "Investigation started, initial analysis complete",
  "updated_by": "investigator_001"
}
```

### Get Case Details

Retrieve detailed case information.

```http
GET /cases/{case_id}
```

**Response:**
```json
{
  "case_id": "CASE_20240115_001",
  "victim_id": "VIC_20240115_001",
  "case_title": "Bitcoin Theft Investigation",
  "case_status": "active",
  "progress_percentage": 45,
  "milestones_completed": [
    "Initial analysis",
    "Evidence collection",
    "Address clustering"
  ],
  "next_actions": [
    "Exchange contact",
    "Report generation"
  ],
  "assigned_investigator": "investigator_001",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T14:00:00Z"
}
```

## Communication API

### Send Victim Communication

Send communication to victim with professional templates.

```http
POST /victims/{victim_id}/communications
```

**Request Body:**
```json
{
  "case_id": "CASE_20240115_001",
  "communication_type": "progress_report",
  "priority": "normal",
  "custom_variables": {
    "recent_developments": "New evidence found",
    "key_findings": "Suspicious transaction patterns detected"
  }
}
```

**Response:**
```json
{
  "message_id": "MSG_20240115_001",
  "communication_type": "progress_report",
  "subject": "Investigation Progress Report - Case CASE_20240115_001",
  "delivery_method": "email",
  "status": "sent",
  "sent_at": "2024-01-15T15:00:00Z"
}
```

### Get Communication History

Retrieve communication history for a victim.

```http
GET /victims/{victim_id}/communications?limit=20
```

**Response:**
```json
{
  "communications": [
    {
      "message_id": "MSG_20240115_001",
      "communication_type": "progress_report",
      "subject": "Investigation Progress Report",
      "sent_at": "2024-01-15T15:00:00Z",
      "status": "delivered",
      "read_receipt": true
    }
  ],
  "total_count": 5,
  "unread_count": 0
}
```

## Evidence Collection API

### Create Evidence Item

Create new evidence item for investigation.

```http
POST /investigations/{investigation_id}/evidence
```

**Request Body:**
```json
{
  "evidence_type": "transaction_data",
  "description": "Bitcoin transaction evidence",
  "data": {
    "txid": "abc123def456",
    "amount": 1.5,
    "from_address": "**********************************"
  },
  "victim_id": "VIC_20240115_001",
  "case_id": "CASE_20240115_001",
  "classification": "confidential"
}
```

**Response:**
```json
{
  "evidence_id": "EVIDENCE_20240115_001",
  "evidence_type": "transaction_data",
  "hash_value": "sha256_hash_value",
  "timestamp": "2024-01-15T16:00:00Z",
  "chain_of_custody": [
    {
      "action": "evidence_created",
      "user_id": "investigator_001",
      "timestamp": "2024-01-15T16:00:00Z"
    }
  ]
}
```

### Add Chain of Custody Entry

Add entry to evidence chain of custody.

```http
POST /evidence/{evidence_id}/custody
```

**Request Body:**
```json
{
  "action": "evidence_reviewed",
  "user_id": "supervisor_001",
  "description": "Evidence reviewed and verified",
  "witness_id": "witness_001"
}
```

## Evidence Packaging API

### Create Evidence Package

Create professional evidence package for legal proceedings.

```http
POST /evidence/packages
```

**Request Body:**
```json
{
  "package_format": "legal_package",
  "evidence_items": ["EVIDENCE_20240115_001", "EVIDENCE_20240115_002"],
  "victim_id": "VIC_20240115_001",
  "case_id": "CASE_20240115_001",
  "include_reports": true,
  "digital_signature_type": "combined"
}
```

**Response:**
```json
{
  "package_id": "PKG_20240115_001",
  "package_type": "legal_package",
  "download_url": "https://api.cryptoforensics.com/v3/packages/PKG_20240115_001/download",
  "integrity_hash": "sha256_package_hash",
  "created_at": "2024-01-15T17:00:00Z",
  "expires_at": "2024-01-22T17:00:00Z"
}
```

### Verify Package Integrity

Verify integrity of evidence package.

```http
GET /evidence/packages/{package_id}/verify
```

**Response:**
```json
{
  "package_id": "PKG_20240115_001",
  "integrity_status": "VERIFIED",
  "verification_timestamp": "2024-01-15T18:00:00Z",
  "total_files": 15,
  "verified_files": 15,
  "failed_files": 0
}
```

## Reporting API

### Generate Report

Generate investigation report for different audiences.

```http
POST /reports/generate
```

**Request Body:**
```json
{
  "report_type": "victim_summary",
  "victim_id": "VIC_20240115_001",
  "case_id": "CASE_20240115_001",
  "format": "pdf",
  "include_technical_details": false,
  "victim_friendly": true
}
```

**Response:**
```json
{
  "report_id": "REPORT_20240115_001",
  "report_type": "victim_summary",
  "format": "pdf",
  "download_url": "https://api.cryptoforensics.com/v3/reports/REPORT_20240115_001/download",
  "generated_at": "2024-01-15T19:00:00Z",
  "expires_at": "2024-01-22T19:00:00Z"
}
```

## Security and Compliance API

### Log Security Event

Log security event for audit trail.

```http
POST /security/events
```

**Request Body:**
```json
{
  "event_type": "data_access",
  "user_id": "investigator_001",
  "resource_accessed": "victim_profile",
  "action_performed": "view_profile",
  "victim_id": "VIC_20240115_001",
  "data_classification": "confidential"
}
```

### Get Compliance Report

Generate compliance report for GDPR and legal requirements.

```http
GET /compliance/reports?victim_id=VIC_20240115_001&period=30d
```

## Error Handling

All API endpoints return standard HTTP status codes and error responses:

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid email format",
    "details": {
      "field": "email",
      "value": "invalid-email"
    },
    "timestamp": "2024-01-15T20:00:00Z"
  }
}
```

### Common Error Codes

- `400` - Bad Request (validation errors)
- `401` - Unauthorized (invalid token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (resource not found)
- `429` - Too Many Requests (rate limit exceeded)
- `500` - Internal Server Error

## SDKs and Libraries

### Python SDK

```python
from cryptoforensics_sdk import CryptoForensicsClient

client = CryptoForensicsClient(api_token="your-token")

# Create victim profile
profile = client.victims.create_profile({
    "full_name": "John Doe",
    "email": "<EMAIL>",
    "estimated_loss": 2.5,
    "currency": "BTC"
})

# Create investigation case
case = client.cases.create(profile.victim_id, {
    "case_title": "Bitcoin Theft Investigation",
    "target_addresses": ["**********************************"]
})
```

### JavaScript SDK

```javascript
import { CryptoForensicsClient } from '@cryptoforensics/sdk';

const client = new CryptoForensicsClient({
  apiToken: 'your-token'
});

// Create victim profile
const profile = await client.victims.createProfile({
  fullName: 'John Doe',
  email: '<EMAIL>',
  estimatedLoss: 2.5,
  currency: 'BTC'
});
```

## Webhooks

Configure webhooks to receive real-time notifications:

```http
POST /webhooks
```

**Request Body:**
```json
{
  "url": "https://your-app.com/webhooks/cryptoforensics",
  "events": [
    "case.status_changed",
    "evidence.created",
    "communication.sent"
  ],
  "secret": "webhook-secret"
}
```

## Support

- **API Documentation**: https://docs.cryptoforensics.com/api
- **SDK Documentation**: https://docs.cryptoforensics.com/sdks
- **Support**: <EMAIL>
- **Status Page**: https://status.cryptoforensics.com
