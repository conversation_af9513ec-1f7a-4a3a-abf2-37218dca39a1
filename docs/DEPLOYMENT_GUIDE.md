# CryptoForensics v3.0 Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying CryptoForensics v3.0 in production environments. The platform includes victim-centric investigation management, professional evidence collection, automated reporting, and enterprise-grade security features.

## Prerequisites

### System Requirements

**Minimum Requirements:**
- CPU: 4 cores
- RAM: 8 GB
- Storage: 100 GB SSD
- Network: 1 Gbps

**Recommended Requirements:**
- CPU: 8 cores
- RAM: 16 GB
- Storage: 500 GB SSD
- Network: 10 Gbps

### Software Dependencies

- Docker 20.10+
- Docker Compose 2.0+
- Git
- OpenSSL
- PostgreSQL 15+ (if not using Docker)
- Redis 7+ (if not using Docker)

### Network Requirements

- Outbound HTTPS (443) for blockchain API access
- Inbound HTTPS (443) for web interface
- SMTP access for email notifications
- Optional: VPN access for secure remote administration

## Quick Start

### 1. Clone Repository

```bash
git clone https://github.com/your-org/cryptoforensics.git
cd cryptoforensics
```

### 2. Run Deployment Script

```bash
chmod +x deployment/scripts/deploy.sh
./deployment/scripts/deploy.sh
```

The deployment script will:
- Check prerequisites
- Generate environment configuration
- Create necessary directories
- Generate SSL certificates
- Deploy all services
- Run database migrations
- Configure monitoring

### 3. Access Application

- Web Interface: https://localhost
- API Endpoint: https://localhost/api
- Health Check: http://localhost:8000/health

## Manual Deployment

### 1. Environment Setup

Create environment file:

```bash
cp deployment/.env.example deployment/.env
```

Update the following variables:

```bash
# Database Configuration
DB_PASSWORD=your-secure-password

# Redis Configuration  
REDIS_PASSWORD=your-redis-password

# Security Keys
SECRET_KEY=your-secret-key
ENCRYPTION_KEY=your-encryption-key

# Email Configuration
SMTP_HOST=your-smtp-host
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
```

### 2. SSL Certificates

For production, replace self-signed certificates:

```bash
# Copy your certificates
cp your-cert.pem deployment/nginx/ssl/cert.pem
cp your-key.pem deployment/nginx/ssl/key.pem
```

### 3. Deploy Services

```bash
cd deployment
docker-compose up -d
```

### 4. Verify Deployment

```bash
# Check service status
docker-compose ps

# View logs
docker-compose logs -f

# Test health endpoint
curl http://localhost:8000/health
```

## Configuration

### Application Configuration

Main configuration file: `deployment/config/production.yml`

Key sections:
- **Application**: Server settings, workers, timeouts
- **Database**: Connection pooling, SSL settings
- **Security**: Authentication, encryption, rate limiting
- **Investigation**: Evidence handling, victim management
- **Monitoring**: Health checks, metrics, logging

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `CRYPTOFORENSICS_ENV` | Environment (production) | Yes |
| `DATABASE_URL` | PostgreSQL connection string | Yes |
| `REDIS_URL` | Redis connection string | Yes |
| `SECRET_KEY` | Application secret key | Yes |
| `ENCRYPTION_KEY` | Data encryption key | Yes |
| `SMTP_HOST` | Email server hostname | Yes |
| `SMTP_USER` | Email username | Yes |
| `SMTP_PASSWORD` | Email password | Yes |

### Database Configuration

PostgreSQL settings in `production.yml`:

```yaml
database:
  url: "${DATABASE_URL}"
  pool_size: 20
  max_overflow: 30
  pool_timeout: 30
  pool_recycle: 3600
```

### Security Configuration

Security settings include:

```yaml
security:
  password_policy:
    min_length: 12
    require_uppercase: true
    require_lowercase: true
    require_numbers: true
    require_special_chars: true
  
  session:
    timeout_minutes: 480
    secure_cookies: true
    httponly_cookies: true
  
  rate_limiting:
    enabled: true
    requests_per_minute: 60
    burst_limit: 100
```

## Victim-Centric Features

### Victim Management

Enable victim management features:

```yaml
investigation:
  victim_management:
    enabled: true
    auto_notifications: true
    progress_updates_enabled: true
    communication_encryption: true
    gdpr_compliance: true
```

### Communication System

Configure email notifications:

```yaml
email:
  smtp_host: "${SMTP_HOST}"
  smtp_port: 587
  use_tls: true
  from_email: "<EMAIL>"
  from_name: "CryptoForensics Investigation Team"
```

### Evidence Management

Configure evidence handling:

```yaml
investigation:
  evidence_integrity_checks: true
  blockchain_timestamping: true
  digital_signatures: true
  data_retention_days: 2555  # 7 years
```

## Monitoring and Logging

### Health Checks

Health check endpoint: `http://localhost:8000/health`

Returns:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "services": {
    "database": "healthy",
    "redis": "healthy",
    "blockchain_apis": "healthy"
  }
}
```

### Metrics

Metrics endpoint: `http://localhost:9090/metrics`

Key metrics:
- Investigation count
- Evidence items processed
- Victim communications sent
- Reports generated
- System performance

### Logging

Log files:
- Application: `/app/logs/application.log`
- Security: `/app/logs/security.log`
- Nginx: `/app/logs/nginx/`

Log rotation:
- Max size: 100MB per file
- Retention: 10 files for application, 20 for security

## Backup and Recovery

### Automated Backups

Backups run daily at 2 AM and include:
- Database
- Investigation data
- Evidence files
- Reports
- Configuration

### Manual Backup

```bash
# Create backup
docker-compose exec backup /app/scripts/backup.sh

# List backups
docker-compose exec backup ls -la /backups/

# Restore from backup
docker-compose exec backup /app/scripts/restore.sh backup-file.tar.gz
```

### Backup Configuration

```yaml
backup:
  enabled: true
  schedule: "0 2 * * *"  # Daily at 2 AM
  retention_days: 30
  encryption_enabled: true
  compression_enabled: true
```

## Security Considerations

### Network Security

- Use HTTPS for all communications
- Implement firewall rules
- Consider VPN for administrative access
- Regular security updates

### Data Protection

- Enable encryption at rest
- Use strong passwords
- Implement proper access controls
- Regular security audits

### Compliance

- GDPR compliance enabled by default
- Audit trail for all actions
- Data retention policies
- Chain of custody for evidence

## Troubleshooting

### Common Issues

**Service won't start:**
```bash
# Check logs
docker-compose logs service-name

# Check resource usage
docker stats

# Restart service
docker-compose restart service-name
```

**Database connection issues:**
```bash
# Check database status
docker-compose exec postgres pg_isready

# Check connection string
echo $DATABASE_URL

# Test connection
docker-compose exec cryptoforensics python -c "from cryptoforensics.database import test_connection; test_connection()"
```

**Email notifications not working:**
```bash
# Test SMTP settings
docker-compose exec cryptoforensics python -c "from cryptoforensics.communication import test_email; test_email()"

# Check email logs
docker-compose logs cryptoforensics | grep email
```

### Performance Issues

**High memory usage:**
- Increase container memory limits
- Optimize database queries
- Enable query caching

**Slow response times:**
- Check database performance
- Monitor network latency
- Scale horizontally if needed

### Log Analysis

```bash
# View real-time logs
docker-compose logs -f

# Search for errors
docker-compose logs | grep ERROR

# Check security events
docker-compose exec cryptoforensics tail -f /app/logs/security.log
```

## Maintenance

### Regular Tasks

**Daily:**
- Monitor system health
- Check backup completion
- Review security logs

**Weekly:**
- Update system packages
- Review performance metrics
- Check disk usage

**Monthly:**
- Security audit
- Update dependencies
- Review access logs

### Updates

```bash
# Update deployment
./deployment/scripts/deploy.sh update

# Manual update
cd deployment
docker-compose pull
docker-compose up -d --build
```

## Support

### Documentation

- API Documentation: `/docs/api/`
- User Manual: `/docs/user-manual/`
- Security Guide: `/docs/security/`

### Contact

- Technical Support: <EMAIL>
- Security Issues: <EMAIL>
- General Inquiries: <EMAIL>

### Community

- GitHub Issues: https://github.com/your-org/cryptoforensics/issues
- Documentation: https://docs.cryptoforensics.com
- Community Forum: https://community.cryptoforensics.com
