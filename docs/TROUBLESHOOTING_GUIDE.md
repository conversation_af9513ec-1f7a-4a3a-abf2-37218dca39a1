# CryptoForensics v3.0 Troubleshooting Guide

## Overview

This comprehensive troubleshooting guide provides solutions for common issues encountered with CryptoForensics v3.0, including victim management, investigation workflows, communication systems, and deployment problems.

## Table of Contents

1. [System Startup Issues](#system-startup-issues)
2. [Victim Management Problems](#victim-management-problems)
3. [Investigation Workflow Issues](#investigation-workflow-issues)
4. [Communication System Problems](#communication-system-problems)
5. [Evidence Collection Issues](#evidence-collection-issues)
6. [Database Problems](#database-problems)
7. [Performance Issues](#performance-issues)
8. [Security and Authentication](#security-and-authentication)
9. [Deployment Issues](#deployment-issues)
10. [API and Integration Problems](#api-and-integration-problems)

## System Startup Issues

### Services Won't Start

**Problem**: Docker containers fail to start or crash immediately.

**Diagnosis**:
```bash
# Check container status
docker-compose ps

# View container logs
docker-compose logs cryptoforensics
docker-compose logs postgres
docker-compose logs redis

# Check system resources
docker system df
free -h
df -h
```

**Solutions**:

1. **Insufficient Resources**
   ```bash
   # Check available memory
   free -h
   
   # If low memory, increase swap or add RAM
   sudo fallocate -l 2G /swapfile
   sudo chmod 600 /swapfile
   sudo mkswap /swapfile
   sudo swapon /swapfile
   ```

2. **Port Conflicts**
   ```bash
   # Check port usage
   netstat -tulpn | grep :8000
   netstat -tulpn | grep :5432
   
   # Change ports in docker-compose.yml if needed
   ```

3. **Permission Issues**
   ```bash
   # Fix file permissions
   sudo chown -R $USER:$USER /path/to/cryptoforensics
   chmod +x deployment/scripts/deploy.sh
   ```

### Database Connection Failures

**Problem**: Application cannot connect to PostgreSQL database.

**Diagnosis**:
```bash
# Test database connectivity
docker-compose exec postgres pg_isready -U cryptoforensics

# Check database logs
docker-compose logs postgres

# Test connection from application
docker-compose exec cryptoforensics python -c "
from cryptoforensics.database import test_connection
test_connection()
"
```

**Solutions**:

1. **Database Not Ready**
   ```bash
   # Wait for database to fully start
   sleep 30
   
   # Restart application after database is ready
   docker-compose restart cryptoforensics
   ```

2. **Wrong Connection String**
   ```bash
   # Check environment variables
   docker-compose exec cryptoforensics env | grep DATABASE_URL
   
   # Update .env file with correct database URL
   DATABASE_URL=***************************************************/cryptoforensics
   ```

3. **Database Initialization**
   ```bash
   # Run database migrations
   docker-compose exec cryptoforensics python -m cryptoforensics.database.migrate
   ```

## Victim Management Problems

### Cannot Create Victim Profile

**Problem**: Victim profile creation fails with validation errors.

**Diagnosis**:
```bash
# Check application logs
docker-compose logs cryptoforensics | grep "victim"

# Test with CLI
cryptoforensics victim create-profile --debug
```

**Solutions**:

1. **Validation Errors**
   - Ensure all required fields are provided
   - Check email format validity
   - Verify phone number format
   - Confirm GDPR consent is explicitly given

2. **Database Constraints**
   ```bash
   # Check for duplicate emails
   docker-compose exec postgres psql -U cryptoforensics -c "
   SELECT email, COUNT(*) FROM victim_profiles 
   GROUP BY email HAVING COUNT(*) > 1;
   "
   ```

3. **Folder Creation Issues**
   ```bash
   # Check folder permissions
   ls -la /app/data/investigations/
   
   # Fix permissions if needed
   chmod 755 /app/data/investigations/
   ```

### Victim Profile Not Found

**Problem**: Cannot retrieve existing victim profile.

**Diagnosis**:
```bash
# Search for victim in database
docker-compose exec postgres psql -U cryptoforensics -c "
SELECT victim_id, full_name, email FROM victim_profiles 
WHERE victim_id = 'VIC_ID' OR email = '<EMAIL>';
"

# Check file system
find /app/data/investigations -name "*VIC_ID*" -type d
```

**Solutions**:

1. **Incorrect Victim ID**
   - Verify victim ID format (should start with VIC_)
   - Check for typos in victim ID
   - Use search by email if ID is unknown

2. **Data Corruption**
   ```bash
   # Restore from backup if available
   docker-compose exec backup /app/scripts/restore-victim.sh VIC_ID
   ```

## Investigation Workflow Issues

### Investigation Fails to Start

**Problem**: Investigation process fails to initialize or crashes.

**Diagnosis**:
```bash
# Check investigation logs
docker-compose logs cryptoforensics | grep "investigation"

# Test blockchain connectivity
cryptoforensics test-connectivity --network bitcoin

# Check API limits
cryptoforensics api-status
```

**Solutions**:

1. **Invalid Addresses**
   ```bash
   # Validate Bitcoin addresses
   cryptoforensics validate-address **********************************
   ```

2. **API Rate Limits**
   ```bash
   # Check API usage
   cryptoforensics api-usage
   
   # Wait for rate limit reset or upgrade plan
   ```

3. **Network Connectivity**
   ```bash
   # Test external API connectivity
   curl -I https://blockstream.info/api/
   curl -I https://api.blockcypher.com/v1/btc/main
   ```

### Investigation Stuck or Slow

**Problem**: Investigation process hangs or runs very slowly.

**Diagnosis**:
```bash
# Check investigation progress
cryptoforensics investigation status CASE_ID

# Monitor system resources
top
iotop
```

**Solutions**:

1. **Resource Constraints**
   ```bash
   # Increase investigation limits
   cryptoforensics config set max_depth 3
   cryptoforensics config set max_addresses 500
   ```

2. **Database Performance**
   ```bash
   # Optimize database
   docker-compose exec postgres psql -U cryptoforensics -c "VACUUM ANALYZE;"
   
   # Check slow queries
   docker-compose exec postgres psql -U cryptoforensics -c "
   SELECT query, mean_time, calls FROM pg_stat_statements 
   ORDER BY mean_time DESC LIMIT 10;
   "
   ```

## Communication System Problems

### Email Notifications Not Sending

**Problem**: Victim notifications fail to send via email.

**Diagnosis**:
```bash
# Check email configuration
docker-compose exec cryptoforensics python -c "
from cryptoforensics.communication import test_email_config
test_email_config()
"

# Check SMTP logs
docker-compose logs cryptoforensics | grep "smtp\|email"
```

**Solutions**:

1. **SMTP Configuration**
   ```bash
   # Verify SMTP settings in .env
   SMTP_HOST=smtp.gmail.com
   SMTP_PORT=587
   SMTP_USER=<EMAIL>
   SMTP_PASSWORD=your-app-password
   ```

2. **Authentication Issues**
   - Use app-specific passwords for Gmail
   - Enable "Less secure app access" if required
   - Check 2FA settings

3. **Firewall/Network Issues**
   ```bash
   # Test SMTP connectivity
   telnet smtp.gmail.com 587
   
   # Check firewall rules
   sudo ufw status
   ```

### Communication Templates Not Loading

**Problem**: Email templates fail to load or render incorrectly.

**Diagnosis**:
```bash
# Check template files
ls -la /app/templates/email/

# Test template rendering
cryptoforensics test-template case_created
```

**Solutions**:

1. **Missing Template Files**
   ```bash
   # Copy default templates
   cp -r /app/templates/default/* /app/templates/email/
   ```

2. **Template Syntax Errors**
   ```bash
   # Validate template syntax
   cryptoforensics validate-templates
   ```

## Evidence Collection Issues

### Evidence Integrity Verification Fails

**Problem**: Evidence integrity checks fail or return errors.

**Diagnosis**:
```bash
# Check evidence integrity
cryptoforensics evidence verify EVIDENCE_ID

# Check hash values
cryptoforensics evidence check-hash EVIDENCE_ID
```

**Solutions**:

1. **Hash Mismatch**
   ```bash
   # Regenerate evidence hash
   cryptoforensics evidence rehash EVIDENCE_ID
   
   # Verify chain of custody
   cryptoforensics evidence chain-of-custody EVIDENCE_ID
   ```

2. **Corrupted Evidence Files**
   ```bash
   # Restore from backup
   docker-compose exec backup /app/scripts/restore-evidence.sh EVIDENCE_ID
   ```

### Chain of Custody Issues

**Problem**: Chain of custody entries are missing or invalid.

**Diagnosis**:
```bash
# Review chain of custody
cryptoforensics evidence custody-log EVIDENCE_ID

# Check for gaps in custody chain
cryptoforensics evidence validate-custody EVIDENCE_ID
```

**Solutions**:

1. **Missing Custody Entries**
   ```bash
   # Add missing custody entry
   cryptoforensics evidence add-custody EVIDENCE_ID \
     --action "evidence_reviewed" \
     --user "investigator_1" \
     --description "Retroactive custody entry"
   ```

2. **Invalid Digital Signatures**
   ```bash
   # Regenerate digital signatures
   cryptoforensics evidence resign EVIDENCE_ID
   ```

## Database Problems

### Database Performance Issues

**Problem**: Database queries are slow or timing out.

**Diagnosis**:
```bash
# Check database performance
docker-compose exec postgres psql -U cryptoforensics -c "
SELECT schemaname, tablename, attname, n_distinct, correlation 
FROM pg_stats WHERE tablename IN ('victim_profiles', 'investigation_cases');
"

# Check active connections
docker-compose exec postgres psql -U cryptoforensics -c "
SELECT count(*) FROM pg_stat_activity;
"
```

**Solutions**:

1. **Missing Indexes**
   ```sql
   -- Add indexes for common queries
   CREATE INDEX idx_victim_profiles_email ON victim_profiles(email);
   CREATE INDEX idx_investigation_cases_victim_id ON investigation_cases(victim_id);
   CREATE INDEX idx_evidence_items_case_id ON evidence_items(case_id);
   ```

2. **Database Maintenance**
   ```bash
   # Vacuum and analyze
   docker-compose exec postgres psql -U cryptoforensics -c "VACUUM ANALYZE;"
   
   # Reindex database
   docker-compose exec postgres psql -U cryptoforensics -c "REINDEX DATABASE cryptoforensics;"
   ```

### Database Connection Pool Exhaustion

**Problem**: "Too many connections" or connection pool errors.

**Diagnosis**:
```bash
# Check connection count
docker-compose exec postgres psql -U cryptoforensics -c "
SELECT count(*) as connections, state 
FROM pg_stat_activity 
GROUP BY state;
"
```

**Solutions**:

1. **Increase Connection Limits**
   ```bash
   # Update PostgreSQL configuration
   echo "max_connections = 200" >> /var/lib/postgresql/data/postgresql.conf
   
   # Restart database
   docker-compose restart postgres
   ```

2. **Optimize Connection Pool**
   ```python
   # Update application configuration
   DATABASE_POOL_SIZE = 20
   DATABASE_MAX_OVERFLOW = 30
   DATABASE_POOL_TIMEOUT = 30
   ```

## Performance Issues

### High Memory Usage

**Problem**: Application consumes excessive memory.

**Diagnosis**:
```bash
# Check memory usage
docker stats cryptoforensics

# Profile memory usage
docker-compose exec cryptoforensics python -m memory_profiler /app/scripts/profile_memory.py
```

**Solutions**:

1. **Increase Container Memory**
   ```yaml
   # In docker-compose.yml
   services:
     cryptoforensics:
       deploy:
         resources:
           limits:
             memory: 4G
   ```

2. **Optimize Application**
   ```bash
   # Clear caches
   docker-compose exec redis redis-cli FLUSHALL
   
   # Restart application
   docker-compose restart cryptoforensics
   ```

### Slow API Response Times

**Problem**: API endpoints respond slowly.

**Diagnosis**:
```bash
# Test API performance
curl -w "@curl-format.txt" -o /dev/null -s "http://localhost:8000/api/victims/profiles"

# Check application logs
docker-compose logs cryptoforensics | grep "slow"
```

**Solutions**:

1. **Enable Caching**
   ```python
   # Update configuration
   CACHE_ENABLED = True
   CACHE_DEFAULT_TIMEOUT = 3600
   ```

2. **Database Query Optimization**
   ```bash
   # Analyze slow queries
   cryptoforensics admin slow-queries --threshold 500ms
   ```

## Security and Authentication

### Authentication Failures

**Problem**: Users cannot log in or authentication fails.

**Diagnosis**:
```bash
# Check authentication logs
docker-compose logs cryptoforensics | grep "auth\|login"

# Test authentication
cryptoforensics auth test-login USERNAME
```

**Solutions**:

1. **Password Issues**
   ```bash
   # Reset user password
   cryptoforensics admin reset-password USERNAME
   
   # Check password policy compliance
   cryptoforensics admin check-password-policy
   ```

2. **2FA Problems**
   ```bash
   # Reset 2FA for user
   cryptoforensics admin reset-2fa USERNAME
   ```

### Session Management Issues

**Problem**: User sessions expire unexpectedly or persist too long.

**Diagnosis**:
```bash
# Check session configuration
cryptoforensics config get session_timeout

# List active sessions
cryptoforensics admin list-sessions
```

**Solutions**:

1. **Adjust Session Settings**
   ```bash
   # Update session timeout
   cryptoforensics config set session_timeout 28800  # 8 hours
   
   # Clear expired sessions
   cryptoforensics admin cleanup-sessions
   ```

## Deployment Issues

### Docker Compose Failures

**Problem**: Docker Compose deployment fails.

**Diagnosis**:
```bash
# Check Docker Compose version
docker-compose --version

# Validate compose file
docker-compose config

# Check Docker daemon
docker info
```

**Solutions**:

1. **Version Compatibility**
   ```bash
   # Update Docker Compose
   sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
   sudo chmod +x /usr/local/bin/docker-compose
   ```

2. **Environment Variables**
   ```bash
   # Check .env file
   cat deployment/.env
   
   # Regenerate environment file
   ./deployment/scripts/deploy.sh
   ```

### SSL Certificate Issues

**Problem**: SSL certificates are invalid or expired.

**Diagnosis**:
```bash
# Check certificate validity
openssl x509 -in deployment/nginx/ssl/cert.pem -text -noout

# Test SSL connection
openssl s_client -connect localhost:443
```

**Solutions**:

1. **Regenerate Certificates**
   ```bash
   # Generate new self-signed certificates
   openssl req -x509 -newkey rsa:4096 -keyout deployment/nginx/ssl/key.pem -out deployment/nginx/ssl/cert.pem -days 365 -nodes
   ```

2. **Use Let's Encrypt**
   ```bash
   # Install certbot
   sudo apt install certbot
   
   # Generate certificate
   sudo certbot certonly --standalone -d your-domain.com
   ```

## API and Integration Problems

### API Rate Limiting

**Problem**: API requests are being rate limited.

**Diagnosis**:
```bash
# Check rate limit status
curl -I http://localhost:8000/api/victims/profiles

# Review rate limit configuration
cryptoforensics config get rate_limiting
```

**Solutions**:

1. **Increase Rate Limits**
   ```bash
   # Update rate limiting configuration
   cryptoforensics config set requests_per_minute 120
   cryptoforensics config set burst_limit 200
   ```

2. **Implement Retry Logic**
   ```python
   import time
   import requests
   
   def api_request_with_retry(url, max_retries=3):
       for attempt in range(max_retries):
           response = requests.get(url)
           if response.status_code != 429:
               return response
           time.sleep(2 ** attempt)  # Exponential backoff
       return None
   ```

### External API Connectivity

**Problem**: Cannot connect to external blockchain APIs.

**Diagnosis**:
```bash
# Test external API connectivity
curl -I https://blockstream.info/api/
curl -I https://api.blockcypher.com/v1/btc/main

# Check DNS resolution
nslookup blockstream.info
```

**Solutions**:

1. **Network Configuration**
   ```bash
   # Check firewall rules
   sudo ufw status
   
   # Allow outbound HTTPS
   sudo ufw allow out 443
   ```

2. **Proxy Configuration**
   ```bash
   # Configure proxy if needed
   export https_proxy=http://proxy.company.com:8080
   export http_proxy=http://proxy.company.com:8080
   ```

## Getting Help

### Log Collection

When reporting issues, collect relevant logs:

```bash
# Collect all logs
mkdir -p /tmp/cryptoforensics-logs
docker-compose logs > /tmp/cryptoforensics-logs/docker-compose.log
docker-compose logs cryptoforensics > /tmp/cryptoforensics-logs/application.log
docker-compose logs postgres > /tmp/cryptoforensics-logs/database.log
docker-compose logs redis > /tmp/cryptoforensics-logs/cache.log

# System information
docker info > /tmp/cryptoforensics-logs/docker-info.txt
docker-compose config > /tmp/cryptoforensics-logs/compose-config.yml
df -h > /tmp/cryptoforensics-logs/disk-usage.txt
free -h > /tmp/cryptoforensics-logs/memory-usage.txt

# Create archive
tar -czf cryptoforensics-logs-$(date +%Y%m%d-%H%M%S).tar.gz -C /tmp cryptoforensics-logs/
```

### Support Contacts

- **Technical Support**: <EMAIL>
- **Emergency Support**: +1-800-CRYPTO-HELP
- **Documentation**: https://docs.cryptoforensics.com
- **Community Forum**: https://community.cryptoforensics.com
- **GitHub Issues**: https://github.com/cryptoforensics/issues

### Escalation Process

1. **Level 1**: Self-service troubleshooting (this guide)
2. **Level 2**: Community forum support
3. **Level 3**: Technical support ticket
4. **Level 4**: Emergency support hotline
5. **Level 5**: Professional services engagement
