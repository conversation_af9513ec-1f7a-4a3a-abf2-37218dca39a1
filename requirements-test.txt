# Testing requirements for CryptoForensics v3.0
# Comprehensive testing suite for victim-centric investigation management

# Core testing framework
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
pytest-xdist>=3.3.0

# Performance testing
pytest-benchmark>=4.0.0
pytest-timeout>=2.1.0

# Code quality and linting
flake8>=6.0.0
mypy>=1.5.0
black>=23.7.0
isort>=5.12.0
bandit>=1.7.5

# Security testing
safety>=2.3.0
semgrep>=1.35.0

# Test data and mocking
factory-boy>=3.3.0
faker>=19.3.0
responses>=0.23.0
freezegun>=1.2.0

# Coverage and reporting
coverage>=7.3.0
pytest-html>=3.2.0
pytest-json-report>=1.5.0

# Documentation testing
pytest-doctestplus>=1.0.0

# Database testing (if needed)
pytest-postgresql>=5.0.0
pytest-redis>=3.0.0

# API testing
httpx>=0.24.0
aioresponses>=0.7.0

# Async testing utilities
asynctest>=0.13.0
pytest-asyncio-cooperative>=0.21.0

# Performance profiling
py-spy>=0.3.14
memory-profiler>=0.61.0

# Load testing
locust>=2.16.0

# Test utilities
testfixtures>=7.2.0
parameterized>=0.9.0

# Visualization testing
matplotlib>=3.7.0
plotly>=5.15.0

# Cryptographic testing
cryptography>=41.0.0
pycryptodome>=3.18.0

# Network testing
aiohttp>=3.8.0
requests-mock>=1.11.0

# File system testing
pyfakefs>=5.2.0

# Time and date testing
python-dateutil>=2.8.0
pytz>=2023.3

# Configuration testing
pydantic>=2.0.0
pyyaml>=6.0

# Logging testing
structlog>=23.1.0

# Email testing
aiosmtpd>=1.4.0

# Development dependencies
pre-commit>=3.3.0
tox>=4.6.0

# Documentation
sphinx>=7.1.0
sphinx-rtd-theme>=1.3.0

# Jupyter for analysis
jupyter>=1.0.0
ipython>=8.14.0

# Data analysis for test results
pandas>=2.0.0
numpy>=1.24.0

# Visualization for test reports
seaborn>=0.12.0
plotly-dash>=2.12.0
