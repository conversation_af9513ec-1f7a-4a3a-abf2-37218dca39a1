#!/usr/bin/env python3
"""
Dependency Fix Script for CryptoForensics

This script identifies and fixes missing dependencies, removes unused imports,
and optimizes the codebase for better performance and maintainability.
"""

import os
import sys
import ast
import importlib
import subprocess
from pathlib import Path
from typing import Set, List, Dict, Any

def find_all_imports(directory: Path) -> Dict[str, Set[str]]:
    """Find all import statements in Python files."""
    imports_by_file = {}
    
    for py_file in directory.rglob("*.py"):
        if py_file.name.startswith('.') or 'venv' in str(py_file) or '__pycache__' in str(py_file):
            continue
            
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            imports = set()
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.add(alias.name.split('.')[0])
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        imports.add(node.module.split('.')[0])
            
            imports_by_file[str(py_file.relative_to(directory))] = imports
            
        except Exception as e:
            print(f"Warning: Could not parse {py_file}: {e}")
    
    return imports_by_file

def get_stdlib_modules() -> Set[str]:
    """Get set of standard library modules."""
    stdlib_modules = set()
    
    # Common stdlib modules
    common_stdlib = {
        'os', 'sys', 'json', 'time', 'datetime', 'logging', 'pathlib', 'typing',
        'collections', 'itertools', 'functools', 'hashlib', 'uuid', 're',
        'argparse', 'subprocess', 'threading', 'asyncio', 'weakref', 'gc',
        'statistics', 'dataclasses', 'enum', 'abc', 'warnings', 'traceback',
        'tempfile', 'shutil', 'glob', 'csv', 'sqlite3', 'urllib', 'http',
        'email', 'base64', 'hmac', 'secrets', 'ssl', 'socket', 'zipfile'
    }
    
    stdlib_modules.update(common_stdlib)
    return stdlib_modules

def check_package_availability(package: str) -> bool:
    """Check if a package is available for import."""
    try:
        importlib.import_module(package)
        return True
    except ImportError:
        return False

def analyze_dependencies(project_root: Path) -> Dict[str, Any]:
    """Analyze project dependencies."""
    print("🔍 Analyzing project dependencies...")
    
    # Find all imports
    imports_by_file = find_all_imports(project_root)
    
    # Get all unique imports
    all_imports = set()
    for imports in imports_by_file.values():
        all_imports.update(imports)
    
    # Categorize imports
    stdlib_modules = get_stdlib_modules()
    external_packages = all_imports - stdlib_modules
    
    # Check availability
    available_packages = set()
    missing_packages = set()
    
    for package in external_packages:
        if check_package_availability(package):
            available_packages.add(package)
        else:
            missing_packages.add(package)
    
    # Read current requirements
    requirements_file = project_root / "requirements.txt"
    current_requirements = set()
    
    if requirements_file.exists():
        with open(requirements_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    package_name = line.split('>=')[0].split('==')[0].split('[')[0]
                    current_requirements.add(package_name)
    
    return {
        'imports_by_file': imports_by_file,
        'all_imports': all_imports,
        'stdlib_modules': stdlib_modules & all_imports,
        'external_packages': external_packages,
        'available_packages': available_packages,
        'missing_packages': missing_packages,
        'current_requirements': current_requirements,
        'unused_requirements': current_requirements - external_packages
    }

def create_minimal_requirements(analysis: Dict[str, Any], output_file: Path):
    """Create a minimal requirements.txt with only necessary dependencies."""
    print("📝 Creating minimal requirements.txt...")
    
    # Essential packages that are actually used
    essential_packages = {
        'requests': '>=2.31.0',
        'networkx': '>=3.1',
        'plotly': '>=5.15.0',
        'tqdm': '>=4.65.0',
        'click': '>=8.1.0',
        'rich': '>=13.0.0',
        'cryptography': '>=41.0.0',
        'pydantic': '>=2.0.0',
        'numpy': '>=1.24.0',
        'pandas': '>=2.0.0',
        'psutil': '>=5.9.0'
    }
    
    # Optional packages for advanced features
    optional_packages = {
        'aiohttp': '>=3.8.0',
        'scipy': '>=1.10.0',
        'scikit-learn': '>=1.3.0',
        'matplotlib': '>=3.7.0',
        'seaborn': '>=0.12.0',
        'pyyaml': '>=6.0',
        'python-dotenv': '>=1.0.0',
        'structlog': '>=23.0.0'
    }
    
    # Check which packages are actually imported
    used_packages = analysis['external_packages']
    
    minimal_requirements = []
    minimal_requirements.append("# Core dependencies for CryptoForensics")
    
    for package, version in essential_packages.items():
        if package in used_packages or package.replace('-', '_') in used_packages:
            minimal_requirements.append(f"{package}{version}")
    
    minimal_requirements.append("\n# Optional dependencies")
    for package, version in optional_packages.items():
        if package in used_packages or package.replace('-', '_') in used_packages:
            minimal_requirements.append(f"{package}{version}")
    
    # Write minimal requirements
    with open(output_file, 'w') as f:
        f.write('\n'.join(minimal_requirements))
    
    print(f"✅ Minimal requirements written to {output_file}")

def fix_import_issues(project_root: Path, analysis: Dict[str, Any]):
    """Fix common import issues in the codebase."""
    print("🔧 Fixing import issues...")
    
    # Files with potential issues
    problematic_files = []
    
    for file_path, imports in analysis['imports_by_file'].items():
        missing_in_file = imports & analysis['missing_packages']
        if missing_in_file:
            problematic_files.append((file_path, missing_in_file))
    
    if problematic_files:
        print("⚠️  Files with missing dependencies:")
        for file_path, missing in problematic_files:
            print(f"  {file_path}: {', '.join(missing)}")
    
    return problematic_files

def main():
    """Main function."""
    project_root = Path(__file__).parent
    
    print("🚀 Starting dependency analysis and fixes...")
    print(f"📁 Project root: {project_root}")
    
    # Analyze dependencies
    analysis = analyze_dependencies(project_root)
    
    print(f"\n📊 Analysis Results:")
    print(f"  Total imports found: {len(analysis['all_imports'])}")
    print(f"  Standard library modules: {len(analysis['stdlib_modules'])}")
    print(f"  External packages: {len(analysis['external_packages'])}")
    print(f"  Available packages: {len(analysis['available_packages'])}")
    print(f"  Missing packages: {len(analysis['missing_packages'])}")
    print(f"  Unused requirements: {len(analysis['unused_requirements'])}")
    
    if analysis['missing_packages']:
        print(f"\n❌ Missing packages: {', '.join(sorted(analysis['missing_packages']))}")
    
    if analysis['unused_requirements']:
        print(f"\n🗑️  Unused requirements: {', '.join(sorted(analysis['unused_requirements']))}")
    
    # Create minimal requirements
    minimal_req_file = project_root / "requirements-minimal.txt"
    create_minimal_requirements(analysis, minimal_req_file)
    
    # Fix import issues
    problematic_files = fix_import_issues(project_root, analysis)
    
    print("\n✅ Dependency analysis complete!")
    print(f"📄 Minimal requirements saved to: {minimal_req_file}")
    
    if problematic_files:
        print("\n💡 Recommendations:")
        print("1. Install missing dependencies or make imports optional")
        print("2. Review files with missing dependencies")
        print("3. Consider using try/except blocks for optional imports")

if __name__ == "__main__":
    main()
