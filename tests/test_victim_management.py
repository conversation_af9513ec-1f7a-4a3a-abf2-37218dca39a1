"""
Comprehensive test suite for victim-centric investigation management system.

Tests victim profile creation, case management, communication, evidence collection,
and integration with existing v3.0 systems.
"""

import pytest
import asyncio
import tempfile
import json
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock

from cryptoforensics.core.config import InvestigationConfig, GlobalConfig
from cryptoforensics.investigation.victim_management import (
    VictimInvestigationManager, VictimProfile, InvestigationCase,
    CaseStatus, CommunicationStatus, DataProtectionLevel
)
from cryptoforensics.communication.victim_communication import (
    VictimCommunicationManager, CommunicationType, DeliveryMethod, NotificationPriority
)
from cryptoforensics.evidence.victim_evidence import (
    VictimEvidenceCollector, VictimEvidenceType, LegalAdmissibilityLevel
)
from cryptoforensics.reporting.victim_reports import (
    VictimReportGenerator, ReportType, ReportFormat
)
from cryptoforensics.evidence.evidence_packaging import (
    EvidencePackagingManager, PackageFormat
)
from cryptoforensics.security.enterprise_security import (
    EnterpriseSecurityManager, SecurityEvent, DataClassification
)
from cryptoforensics.exceptions import InvestigationError, CommunicationError, EvidenceError


class TestVictimInvestigationManager:
    """Test suite for VictimInvestigationManager."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = InvestigationConfig()
            config.output_directory = temp_dir
            yield config
    
    @pytest.fixture
    def victim_manager(self, config):
        """Create VictimInvestigationManager instance."""
        return VictimInvestigationManager(config)
    
    @pytest.fixture
    def sample_victim_data(self):
        """Sample victim data for testing."""
        return {
            "full_name": "John Doe",
            "email": "<EMAIL>",
            "phone": "******-0123",
            "incident_description": "Cryptocurrency theft from wallet",
            "estimated_loss": 2.5,
            "currency": "BTC",
            "affected_addresses": ["**********************************"],
            "gdpr_consent": True,
            "created_by": "test_user"
        }
    
    @pytest.mark.asyncio
    async def test_create_victim_profile(self, victim_manager, sample_victim_data):
        """Test victim profile creation."""
        profile = await victim_manager.create_victim_profile_async(sample_victim_data)
        
        assert profile.victim_id.startswith("VIC_")
        assert profile.full_name == "John Doe"
        assert profile.email == "<EMAIL>"
        assert profile.estimated_loss == 2.5
        assert profile.currency == "BTC"
        assert profile.gdpr_consent is True
        assert len(profile.affected_addresses) == 1
        assert profile.communication_status == CommunicationStatus.INITIAL_CONTACT
    
    @pytest.mark.asyncio
    async def test_create_investigation_case(self, victim_manager, sample_victim_data):
        """Test investigation case creation."""
        # Create victim profile first
        profile = await victim_manager.create_victim_profile_async(sample_victim_data)
        
        case_data = {
            "case_title": "Bitcoin Theft Investigation",
            "case_description": "Investigation of stolen Bitcoin",
            "case_priority": "high",
            "target_addresses": ["**********************************"],
            "investigation_depth": 3,
            "assigned_investigator": "investigator_1"
        }
        
        case = await victim_manager.create_investigation_case_async(profile.victim_id, case_data)
        
        assert case.case_id.startswith("CASE_")
        assert case.victim_id == profile.victim_id
        assert case.case_title == "Bitcoin Theft Investigation"
        assert case.case_status == CaseStatus.INTAKE
        assert case.progress_percentage == 10  # Initial progress
        assert len(case.target_addresses) == 1
        assert case.case_folder_path is not None
    
    @pytest.mark.asyncio
    async def test_update_case_status(self, victim_manager, sample_victim_data):
        """Test case status updates."""
        profile = await victim_manager.create_victim_profile_async(sample_victim_data)
        case_data = {"case_title": "Test Case", "case_description": "Test"}
        case = await victim_manager.create_investigation_case_async(profile.victim_id, case_data)
        
        # Update status to active
        await victim_manager.update_case_status_async(
            case.case_id, CaseStatus.ACTIVE, "Investigation started", "test_user"
        )
        
        updated_case = await victim_manager.get_investigation_case_async(case.case_id)
        assert updated_case.case_status == CaseStatus.ACTIVE
        assert updated_case.progress_percentage == 30
    
    @pytest.mark.asyncio
    async def test_case_summary_generation(self, victim_manager, sample_victim_data):
        """Test case summary generation."""
        profile = await victim_manager.create_victim_profile_async(sample_victim_data)
        case_data = {"case_title": "Test Case", "case_description": "Test"}
        case = await victim_manager.create_investigation_case_async(profile.victim_id, case_data)
        
        summary = victim_manager.generate_case_summary(case.case_id)
        
        assert "case_id" in summary
        assert "victim_id" in summary
        assert "case_title" in summary
        assert "victim_info" in summary
        assert "investigation_details" in summary
        assert "communication_stats" in summary
        assert "folder_structure" in summary
    
    def test_invalid_victim_data(self, victim_manager):
        """Test handling of invalid victim data."""
        invalid_data = {"full_name": ""}  # Missing required fields
        
        with pytest.raises(InvestigationError):
            asyncio.run(victim_manager.create_victim_profile_async(invalid_data))


class TestVictimCommunicationManager:
    """Test suite for VictimCommunicationManager."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        config = GlobalConfig()
        config.investigation = InvestigationConfig()
        with tempfile.TemporaryDirectory() as temp_dir:
            config.investigation.output_directory = temp_dir
            yield config
    
    @pytest.fixture
    def comm_manager(self, config):
        """Create VictimCommunicationManager instance."""
        return VictimCommunicationManager(config)
    
    @pytest.fixture
    def sample_victim_profile(self):
        """Sample victim profile for testing."""
        return VictimProfile(
            victim_id="VIC_TEST_001",
            full_name="Jane Smith",
            email="<EMAIL>",
            preferred_contact_method="email",
            estimated_loss=1.5,
            currency="BTC",
            incident_date=datetime.now(),
            gdpr_consent=True
        )
    
    @pytest.fixture
    def sample_case(self):
        """Sample investigation case for testing."""
        return InvestigationCase(
            case_id="CASE_TEST_001",
            victim_id="VIC_TEST_001",
            case_title="Test Investigation",
            case_description="Test case for communication",
            case_status=CaseStatus.ACTIVE,
            assigned_investigator="Test Investigator",
            progress_percentage=50,
            milestones_completed=["Initial analysis", "Evidence collection"],
            next_actions=["Generate report", "Contact exchanges"]
        )
    
    @pytest.mark.asyncio
    async def test_send_case_created_notification(self, comm_manager, sample_victim_profile, sample_case):
        """Test case creation notification."""
        with patch.object(comm_manager, '_send_email', new_callable=AsyncMock) as mock_send:
            message = await comm_manager.send_victim_communication_async(
                victim_profile=sample_victim_profile,
                case=sample_case,
                communication_type=CommunicationType.CASE_CREATED
            )
            
            assert message.communication_type == CommunicationType.CASE_CREATED
            assert message.victim_id == "VIC_TEST_001"
            assert message.case_id == "CASE_TEST_001"
            assert "Case Created" in message.subject
            assert sample_victim_profile.full_name in message.content
    
    @pytest.mark.asyncio
    async def test_send_progress_update(self, comm_manager, sample_victim_profile, sample_case):
        """Test progress update notification."""
        custom_variables = {
            "recent_developments": "New evidence found",
            "key_findings": "Suspicious transaction patterns detected"
        }
        
        message = await comm_manager.send_victim_communication_async(
            victim_profile=sample_victim_profile,
            case=sample_case,
            communication_type=CommunicationType.PROGRESS_REPORT,
            custom_variables=custom_variables
        )
        
        assert message.communication_type == CommunicationType.PROGRESS_REPORT
        assert "Progress Report" in message.subject
        assert "50%" in message.content  # Progress percentage
        assert "New evidence found" in message.content
    
    @pytest.mark.asyncio
    async def test_schedule_automatic_updates(self, comm_manager):
        """Test automatic update scheduling."""
        scheduled_messages = await comm_manager.schedule_automatic_updates_async(
            "VIC_TEST_001", "CASE_TEST_001"
        )
        
        assert len(scheduled_messages) == 4  # 4 weeks of updates
        for message in scheduled_messages:
            assert message.communication_type == CommunicationType.PROGRESS_REPORT
            assert message.scheduled_at is not None
    
    @pytest.mark.asyncio
    async def test_communication_history(self, comm_manager, sample_victim_profile, sample_case):
        """Test communication history tracking."""
        # Send multiple messages
        await comm_manager.send_victim_communication_async(
            sample_victim_profile, sample_case, CommunicationType.CASE_CREATED
        )
        await comm_manager.send_victim_communication_async(
            sample_victim_profile, sample_case, CommunicationType.STATUS_UPDATE
        )
        
        history = await comm_manager.get_communication_history_async("VIC_TEST_001", limit=10)
        
        assert len(history) == 2
        assert history[0].timestamp > history[1].timestamp  # Newest first
    
    @pytest.mark.asyncio
    async def test_communication_report_generation(self, comm_manager, sample_victim_profile, sample_case):
        """Test communication report generation."""
        # Send some messages
        await comm_manager.send_victim_communication_async(
            sample_victim_profile, sample_case, CommunicationType.CASE_CREATED
        )
        
        report = await comm_manager.generate_communication_report_async("VIC_TEST_001")
        
        assert "report_metadata" in report
        assert "communication_summary" in report
        assert "recent_communications" in report
        assert report["communication_summary"]["total_messages"] >= 1


class TestVictimEvidenceCollector:
    """Test suite for VictimEvidenceCollector."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        config = InvestigationConfig()
        with tempfile.TemporaryDirectory() as temp_dir:
            config.output_directory = temp_dir
            config.evidence_integrity_checks = True
            yield config
    
    @pytest.fixture
    def evidence_collector(self, config):
        """Create VictimEvidenceCollector instance."""
        return VictimEvidenceCollector("TEST_INVESTIGATION", config, "VIC_TEST_001", "CASE_TEST_001")
    
    @pytest.mark.asyncio
    async def test_create_victim_evidence(self, evidence_collector):
        """Test victim evidence creation."""
        evidence_data = {
            "incident_report": "Detailed incident description",
            "loss_amount": 2.5,
            "affected_addresses": ["**********************************"]
        }
        
        evidence = await evidence_collector.create_victim_evidence_async(
            evidence_type=VictimEvidenceType.INCIDENT_REPORT,
            description="Victim incident report",
            data=evidence_data,
            victim_consent=True,
            legal_admissibility=LegalAdmissibilityLevel.COURT_READY
        )
        
        assert evidence.evidence_id is not None
        assert evidence.victim_id == "VIC_TEST_001"
        assert evidence.case_id == "CASE_TEST_001"
        assert evidence.evidence_category == VictimEvidenceType.INCIDENT_REPORT
        assert evidence.legal_admissibility == LegalAdmissibilityLevel.COURT_READY
        assert evidence.victim_consent is True
        assert len(evidence.chain_of_custody) == 1
    
    @pytest.mark.asyncio
    async def test_chain_of_custody(self, evidence_collector):
        """Test chain of custody functionality."""
        evidence_data = {"test": "data"}
        evidence = await evidence_collector.create_victim_evidence_async(
            VictimEvidenceType.VICTIM_STATEMENT, "Test evidence", evidence_data
        )
        
        # Add custody entry
        custody_entry = await evidence_collector.add_victim_custody_entry_async(
            evidence.evidence_id,
            "evidence_reviewed",
            "investigator_1",
            "Evidence reviewed by investigator",
            witness_id="witness_1"
        )
        
        assert custody_entry.evidence_id == evidence.evidence_id
        assert custody_entry.action == "evidence_reviewed"
        assert custody_entry.user_id == "investigator_1"
        assert custody_entry.witness_id == "witness_1"
        assert custody_entry.digital_signature is not None
    
    @pytest.mark.asyncio
    async def test_evidence_integrity_verification(self, evidence_collector):
        """Test evidence integrity verification."""
        evidence_data = {"test": "data"}
        evidence = await evidence_collector.create_victim_evidence_async(
            VictimEvidenceType.VICTIM_STATEMENT, "Test evidence", evidence_data
        )
        
        verification = await evidence_collector.verify_victim_evidence_integrity_async(evidence.evidence_id)
        
        assert verification["integrity_status"] == "VERIFIED"
        assert verification["hash_verification"]["status"] == "VALID"
        assert verification["chain_of_custody"]["status"] == "VALID"
        assert verification["legal_compliance"]["compliant"] is True
    
    @pytest.mark.asyncio
    async def test_evidence_report_generation(self, evidence_collector):
        """Test evidence report generation."""
        # Create multiple evidence items
        for i in range(3):
            await evidence_collector.create_victim_evidence_async(
                VictimEvidenceType.VICTIM_STATEMENT,
                f"Test evidence {i}",
                {"test": f"data_{i}"}
            )
        
        report = await evidence_collector.generate_victim_evidence_report_async()
        
        assert "evidence_summary" in report
        assert "compliance_summary" in report
        assert "chain_of_custody_summary" in report
        assert report["evidence_summary"]["total_evidence_items"] == 3
    
    @pytest.mark.asyncio
    async def test_evidence_export(self, evidence_collector):
        """Test evidence package export."""
        evidence_data = {"test": "data"}
        await evidence_collector.create_victim_evidence_async(
            VictimEvidenceType.VICTIM_STATEMENT, "Test evidence", evidence_data
        )
        
        export_path = await evidence_collector.export_victim_evidence_package_async()
        
        assert Path(export_path).exists()
        assert export_path.endswith('.json')


class TestIntegrationWorkflow:
    """Integration tests for complete victim-centric workflow."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        config = GlobalConfig()
        config.investigation = InvestigationConfig()
        with tempfile.TemporaryDirectory() as temp_dir:
            config.investigation.output_directory = temp_dir
            config.investigation.evidence_integrity_checks = True
            yield config
    
    @pytest.mark.asyncio
    async def test_complete_victim_workflow(self, config):
        """Test complete victim investigation workflow."""
        # Initialize managers
        victim_manager = VictimInvestigationManager(config.investigation)
        comm_manager = VictimCommunicationManager(config)
        evidence_collector = VictimEvidenceCollector("TEST_INVESTIGATION", config.investigation)
        
        # Step 1: Create victim profile
        victim_data = {
            "full_name": "Integration Test Victim",
            "email": "<EMAIL>",
            "incident_description": "Test incident",
            "estimated_loss": 1.0,
            "currency": "BTC",
            "affected_addresses": ["**********************************"],
            "gdpr_consent": True
        }
        
        profile = await victim_manager.create_victim_profile_async(victim_data)
        assert profile.victim_id is not None
        
        # Step 2: Create investigation case
        case_data = {
            "case_title": "Integration Test Case",
            "case_description": "Test case for integration",
            "target_addresses": ["**********************************"]
        }
        
        case = await victim_manager.create_investigation_case_async(profile.victim_id, case_data)
        assert case.case_id is not None
        assert case.case_folder_path is not None
        
        # Step 3: Send case creation notification
        with patch.object(comm_manager, '_send_email', new_callable=AsyncMock):
            message = await comm_manager.send_victim_communication_async(
                profile, case, CommunicationType.CASE_CREATED
            )
            assert message.communication_type == CommunicationType.CASE_CREATED
        
        # Step 4: Collect evidence
        evidence_collector.victim_id = profile.victim_id
        evidence_collector.case_id = case.case_id
        
        evidence = await evidence_collector.create_victim_evidence_async(
            VictimEvidenceType.INCIDENT_REPORT,
            "Integration test evidence",
            {"incident": "test data"}
        )
        assert evidence.victim_id == profile.victim_id
        assert evidence.case_id == case.case_id
        
        # Step 5: Update case status
        await victim_manager.update_case_status_async(
            case.case_id, CaseStatus.COMPLETED, "Integration test completed"
        )
        
        updated_case = await victim_manager.get_investigation_case_async(case.case_id)
        assert updated_case.case_status == CaseStatus.COMPLETED
        
        # Step 6: Generate case summary
        summary = victim_manager.generate_case_summary(case.case_id)
        assert summary["case_id"] == case.case_id
        assert summary["victim_info"]["victim_id"] == profile.victim_id


class TestPerformanceAndSecurity:
    """Performance and security validation tests."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        config = GlobalConfig()
        config.investigation = InvestigationConfig()
        with tempfile.TemporaryDirectory() as temp_dir:
            config.investigation.output_directory = temp_dir
            yield config
    
    @pytest.mark.asyncio
    async def test_concurrent_victim_creation(self, config):
        """Test concurrent victim profile creation."""
        victim_manager = VictimInvestigationManager(config.investigation)
        
        async def create_victim(index):
            victim_data = {
                "full_name": f"Test Victim {index}",
                "email": f"test{index}@example.com",
                "incident_description": f"Test incident {index}",
                "estimated_loss": 1.0,
                "currency": "BTC",
                "gdpr_consent": True
            }
            return await victim_manager.create_victim_profile_async(victim_data)
        
        # Create 10 victims concurrently
        tasks = [create_victim(i) for i in range(10)]
        profiles = await asyncio.gather(*tasks)
        
        assert len(profiles) == 10
        assert len(set(p.victim_id for p in profiles)) == 10  # All unique IDs
    
    @pytest.mark.asyncio
    async def test_security_logging(self, config):
        """Test security event logging."""
        security_manager = EnterpriseSecurityManager(config)
        
        # Log security event
        log_entry = await security_manager.log_security_event_async(
            event_type=SecurityEvent.DATA_ACCESS,
            user_id="test_user",
            session_id="test_session",
            resource_accessed="victim_profile",
            action_performed="view_profile",
            result="success",
            data_classification=DataClassification.CONFIDENTIAL
        )
        
        assert log_entry.event_type == SecurityEvent.DATA_ACCESS
        assert log_entry.user_id == "test_user"
        assert log_entry.data_classification == DataClassification.CONFIDENTIAL
        assert "GDPR_PERSONAL_DATA" in log_entry.compliance_tags
    
    def test_data_validation(self, config):
        """Test data validation and sanitization."""
        victim_manager = VictimInvestigationManager(config.investigation)
        
        # Test with malicious input
        malicious_data = {
            "full_name": "<script>alert('xss')</script>",
            "email": "<EMAIL>",
            "incident_description": "'; DROP TABLE victims; --",
            "estimated_loss": -1.0,  # Invalid negative amount
            "currency": "INVALID_CURRENCY",
            "gdpr_consent": True
        }
        
        # Should handle malicious input gracefully
        with pytest.raises(InvestigationError):
            asyncio.run(victim_manager.create_victim_profile_async(malicious_data))


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
