"""
Pytest configuration and shared fixtures for CryptoForensics v3.0 test suite.

Provides common test fixtures, configuration, and utilities for testing
victim-centric investigation management features.
"""

import pytest
import asyncio
import tempfile
import logging
from pathlib import Path
from unittest.mock import Mock, patch
from typing import Dict, Any

from cryptoforensics.core.config import InvestigationConfig, GlobalConfig
from cryptoforensics.investigation.victim_management import <PERSON><PERSON>mProfile, InvestigationCase, CaseStatus
from cryptoforensics.evidence.collector import EvidenceItem
from cryptoforensics.evidence.victim_evidence import VictimEvidenceItem, VictimEvidenceType


# Configure logging for tests
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def temp_directory():
    """Create a temporary directory for test files."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def test_config(temp_directory):
    """Create test configuration with temporary directory."""
    config = InvestigationConfig()
    config.output_directory = str(temp_directory)
    config.evidence_integrity_checks = True
    config.max_depth = 3
    config.max_addresses = 100
    config.max_transactions = 500
    return config


@pytest.fixture
def test_global_config(temp_directory):
    """Create test global configuration."""
    config = GlobalConfig()
    config.investigation = InvestigationConfig()
    config.investigation.output_directory = str(temp_directory)
    config.investigation.evidence_integrity_checks = True
    config.debug_mode = True
    config.environment = "testing"
    return config


@pytest.fixture
def sample_victim_profile():
    """Create a sample victim profile for testing."""
    return VictimProfile(
        victim_id="VIC_TEST_001",
        full_name="John Doe",
        email="<EMAIL>",
        phone="******-0123",
        preferred_contact_method="email",
        incident_description="Cryptocurrency theft from wallet",
        incident_date="2024-01-15T10:00:00Z",
        estimated_loss=2.5,
        currency="BTC",
        affected_addresses=["**********************************"],
        gdpr_consent=True,
        data_protection_level="high",
        created_by="test_user",
        created_at="2024-01-15T10:00:00Z"
    )


@pytest.fixture
def sample_investigation_case(sample_victim_profile):
    """Create a sample investigation case for testing."""
    return InvestigationCase(
        case_id="CASE_TEST_001",
        victim_id=sample_victim_profile.victim_id,
        case_title="Test Investigation Case",
        case_description="Test case for unit testing",
        case_status=CaseStatus.ACTIVE,
        case_priority="high",
        target_addresses=["**********************************"],
        investigation_depth=3,
        assigned_investigator="test_investigator",
        created_by="test_user",
        created_at="2024-01-15T10:00:00Z",
        progress_percentage=50,
        milestones_completed=["Initial analysis", "Evidence collection"],
        next_actions=["Generate report", "Contact exchanges"],
        case_folder_path="/test/investigations/VIC_TEST_001/CASE_TEST_001"
    )


@pytest.fixture
def sample_evidence_items():
    """Create sample evidence items for testing."""
    return [
        EvidenceItem(
            evidence_id="EVIDENCE_001",
            investigation_id="TEST_INVESTIGATION",
            evidence_type="transaction_data",
            description="Bitcoin transaction evidence",
            data={
                "txid": "abc123def456",
                "amount": 1.5,
                "from_address": "**********************************",
                "to_address": "**********************************"
            },
            timestamp="2024-01-15T10:00:00Z",
            hash_value="sha256_hash_001",
            tags=["bitcoin", "transaction", "suspicious"],
            classification="confidential",
            chain_of_custody=[{
                "action": "evidence_created",
                "user_id": "system",
                "timestamp": "2024-01-15T10:00:00Z",
                "description": "Evidence automatically collected"
            }]
        ),
        EvidenceItem(
            evidence_id="EVIDENCE_002",
            investigation_id="TEST_INVESTIGATION",
            evidence_type="address_analysis",
            description="Address clustering analysis",
            data={
                "addresses": ["**********************************", "**********************************"],
                "cluster_id": "cluster_001",
                "risk_score": 0.75
            },
            timestamp="2024-01-15T11:00:00Z",
            hash_value="sha256_hash_002",
            tags=["address", "clustering", "analysis"],
            classification="confidential",
            chain_of_custody=[{
                "action": "evidence_created",
                "user_id": "investigator_1",
                "timestamp": "2024-01-15T11:00:00Z",
                "description": "Address analysis completed"
            }]
        )
    ]


@pytest.fixture
def sample_victim_evidence_items(sample_victim_profile, sample_investigation_case):
    """Create sample victim evidence items for testing."""
    return [
        VictimEvidenceItem(
            evidence_id="VICTIM_EVIDENCE_001",
            investigation_id="TEST_INVESTIGATION",
            evidence_type="victim_statement",
            description="Victim incident report",
            data={
                "statement": "I lost 2.5 BTC from my wallet on January 15, 2024",
                "wallet_address": "**********************************",
                "transaction_id": "suspected_theft_tx_001"
            },
            timestamp="2024-01-15T09:00:00Z",
            hash_value="victim_hash_001",
            victim_id=sample_victim_profile.victim_id,
            case_id=sample_investigation_case.case_id,
            evidence_category=VictimEvidenceType.VICTIM_STATEMENT,
            victim_consent=True,
            gdpr_compliant=True,
            retention_period_days=2555,
            chain_of_custody=[{
                "action": "evidence_created",
                "user_id": "victim_intake_specialist",
                "timestamp": "2024-01-15T09:00:00Z",
                "description": "Victim statement collected during intake"
            }]
        ),
        VictimEvidenceItem(
            evidence_id="VICTIM_EVIDENCE_002",
            investigation_id="TEST_INVESTIGATION",
            evidence_type="incident_report",
            description="Detailed incident documentation",
            data={
                "incident_type": "cryptocurrency_theft",
                "loss_amount": 2.5,
                "currency": "BTC",
                "incident_date": "2024-01-15T08:00:00Z",
                "discovery_date": "2024-01-15T08:30:00Z",
                "reported_date": "2024-01-15T09:00:00Z"
            },
            timestamp="2024-01-15T09:30:00Z",
            hash_value="victim_hash_002",
            victim_id=sample_victim_profile.victim_id,
            case_id=sample_investigation_case.case_id,
            evidence_category=VictimEvidenceType.INCIDENT_REPORT,
            victim_consent=True,
            gdpr_compliant=True,
            retention_period_days=2555,
            chain_of_custody=[{
                "action": "evidence_created",
                "user_id": "case_manager",
                "timestamp": "2024-01-15T09:30:00Z",
                "description": "Incident report compiled from victim statement"
            }]
        )
    ]


@pytest.fixture
def mock_email_service():
    """Mock email service for testing communication features."""
    with patch('smtplib.SMTP') as mock_smtp:
        mock_server = Mock()
        mock_smtp.return_value.__enter__.return_value = mock_server
        mock_server.send_message.return_value = {}
        yield mock_server


@pytest.fixture
def mock_blockchain_api():
    """Mock blockchain API responses for testing."""
    mock_responses = {
        "**********************************": {
            "address": "**********************************",
            "balance": 0,
            "total_received": 5000000000,  # 50 BTC in satoshis
            "total_sent": 5000000000,
            "tx_count": 1
        }
    }
    
    with patch('cryptoforensics.api.client.APIClient.get_address_info') as mock_api:
        mock_api.side_effect = lambda addr: mock_responses.get(addr, {})
        yield mock_api


@pytest.fixture
def mock_database():
    """Mock database for testing data persistence."""
    database = {}
    
    def mock_save(key: str, data: Dict[str, Any]):
        database[key] = data
        return True
    
    def mock_load(key: str):
        return database.get(key)
    
    def mock_delete(key: str):
        return database.pop(key, None)
    
    def mock_list_keys(prefix: str = ""):
        return [k for k in database.keys() if k.startswith(prefix)]
    
    with patch('cryptoforensics.database.save') as save_mock, \
         patch('cryptoforensics.database.load') as load_mock, \
         patch('cryptoforensics.database.delete') as delete_mock, \
         patch('cryptoforensics.database.list_keys') as list_mock:
        
        save_mock.side_effect = mock_save
        load_mock.side_effect = mock_load
        delete_mock.side_effect = mock_delete
        list_mock.side_effect = mock_list_keys
        
        yield {
            'save': save_mock,
            'load': load_mock,
            'delete': delete_mock,
            'list_keys': list_mock,
            'data': database
        }


@pytest.fixture
def performance_monitor():
    """Mock performance monitoring for testing."""
    metrics = {}
    
    def mock_start_timer(operation: str):
        metrics[operation] = {"start_time": "2024-01-15T10:00:00Z"}
        return operation
    
    def mock_end_timer(operation: str):
        if operation in metrics:
            metrics[operation]["end_time"] = "2024-01-15T10:00:01Z"
            metrics[operation]["duration"] = 1.0
        return metrics.get(operation, {})
    
    with patch('cryptoforensics.utils.performance.start_timer') as start_mock, \
         patch('cryptoforensics.utils.performance.end_timer') as end_mock:
        
        start_mock.side_effect = mock_start_timer
        end_mock.side_effect = mock_end_timer
        
        yield {
            'start_timer': start_mock,
            'end_timer': end_mock,
            'metrics': metrics
        }


# Test utilities
def assert_valid_uuid(uuid_string: str, prefix: str = ""):
    """Assert that a string is a valid UUID with optional prefix."""
    if prefix:
        assert uuid_string.startswith(prefix), f"UUID should start with {prefix}"
        uuid_part = uuid_string[len(prefix):]
    else:
        uuid_part = uuid_string
    
    # Basic UUID format check (simplified)
    assert len(uuid_part) >= 8, "UUID part should be at least 8 characters"
    assert "_" in uuid_part or "-" in uuid_part, "UUID should contain separators"


def assert_valid_timestamp(timestamp_string: str):
    """Assert that a string is a valid ISO timestamp."""
    from datetime import datetime
    try:
        datetime.fromisoformat(timestamp_string.replace('Z', '+00:00'))
    except ValueError:
        pytest.fail(f"Invalid timestamp format: {timestamp_string}")


def assert_valid_email(email: str):
    """Assert that a string is a valid email address."""
    import re
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    assert re.match(email_pattern, email), f"Invalid email format: {email}"


def assert_secure_data_handling(data: Dict[str, Any]):
    """Assert that data follows secure handling practices."""
    # Check for sensitive data patterns
    sensitive_patterns = ['password', 'secret', 'key', 'token']
    for key, value in data.items():
        if any(pattern in key.lower() for pattern in sensitive_patterns):
            assert isinstance(value, str) and len(value) > 0, f"Sensitive field {key} should not be empty"
            # In real implementation, check for encryption/hashing


# Pytest configuration
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "performance: mark test as performance test"
    )
    config.addinivalue_line(
        "markers", "security: mark test as security test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on test names."""
    for item in items:
        # Add integration marker to integration tests
        if "integration" in item.nodeid.lower():
            item.add_marker(pytest.mark.integration)
        
        # Add performance marker to performance tests
        if "performance" in item.nodeid.lower():
            item.add_marker(pytest.mark.performance)
        
        # Add security marker to security tests
        if "security" in item.nodeid.lower():
            item.add_marker(pytest.mark.security)
        
        # Add slow marker to tests that might be slow
        if any(keyword in item.nodeid.lower() for keyword in ["concurrent", "stress", "load"]):
            item.add_marker(pytest.mark.slow)
