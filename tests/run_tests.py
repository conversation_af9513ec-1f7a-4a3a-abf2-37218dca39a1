#!/usr/bin/env python3
"""
Comprehensive test runner for CryptoForensics v3.0

Runs all test suites including unit tests, integration tests, performance tests,
and security validation for the victim-centric investigation management system.
"""

import sys
import subprocess
import argparse
import time
from pathlib import Path
from typing import List, Dict, Any


class TestRunner:
    """Comprehensive test runner for CryptoForensics v3.0."""
    
    def __init__(self):
        """Initialize test runner."""
        self.test_dir = Path(__file__).parent
        self.project_root = self.test_dir.parent
        self.results = {}
    
    def run_unit_tests(self, verbose: bool = False) -> Dict[str, Any]:
        """Run unit tests for all components."""
        print("🧪 Running Unit Tests...")
        
        cmd = [
            sys.executable, "-m", "pytest",
            str(self.test_dir),
            "-v" if verbose else "-q",
            "--tb=short",
            "-m", "not integration and not performance and not slow",
            "--junitxml=test_results_unit.xml",
            "--cov=cryptoforensics",
            "--cov-report=html:htmlcov_unit",
            "--cov-report=term-missing"
        ]
        
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)
        duration = time.time() - start_time
        
        self.results['unit_tests'] = {
            'success': result.returncode == 0,
            'duration': duration,
            'stdout': result.stdout,
            'stderr': result.stderr
        }
        
        if result.returncode == 0:
            print(f"✅ Unit tests passed in {duration:.2f}s")
        else:
            print(f"❌ Unit tests failed in {duration:.2f}s")
            if verbose:
                print(result.stdout)
                print(result.stderr)
        
        return self.results['unit_tests']
    
    def run_integration_tests(self, verbose: bool = False) -> Dict[str, Any]:
        """Run integration tests."""
        print("🔗 Running Integration Tests...")
        
        cmd = [
            sys.executable, "-m", "pytest",
            str(self.test_dir),
            "-v" if verbose else "-q",
            "--tb=short",
            "-m", "integration",
            "--junitxml=test_results_integration.xml",
            "--cov=cryptoforensics",
            "--cov-report=html:htmlcov_integration",
            "--cov-append"
        ]
        
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)
        duration = time.time() - start_time
        
        self.results['integration_tests'] = {
            'success': result.returncode == 0,
            'duration': duration,
            'stdout': result.stdout,
            'stderr': result.stderr
        }
        
        if result.returncode == 0:
            print(f"✅ Integration tests passed in {duration:.2f}s")
        else:
            print(f"❌ Integration tests failed in {duration:.2f}s")
            if verbose:
                print(result.stdout)
                print(result.stderr)
        
        return self.results['integration_tests']
    
    def run_performance_tests(self, verbose: bool = False) -> Dict[str, Any]:
        """Run performance tests."""
        print("⚡ Running Performance Tests...")
        
        cmd = [
            sys.executable, "-m", "pytest",
            str(self.test_dir),
            "-v" if verbose else "-q",
            "--tb=short",
            "-m", "performance",
            "--junitxml=test_results_performance.xml",
            "--benchmark-only",
            "--benchmark-json=benchmark_results.json"
        ]
        
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)
        duration = time.time() - start_time
        
        self.results['performance_tests'] = {
            'success': result.returncode == 0,
            'duration': duration,
            'stdout': result.stdout,
            'stderr': result.stderr
        }
        
        if result.returncode == 0:
            print(f"✅ Performance tests passed in {duration:.2f}s")
        else:
            print(f"❌ Performance tests failed in {duration:.2f}s")
            if verbose:
                print(result.stdout)
                print(result.stderr)
        
        return self.results['performance_tests']
    
    def run_security_tests(self, verbose: bool = False) -> Dict[str, Any]:
        """Run security validation tests."""
        print("🔒 Running Security Tests...")
        
        cmd = [
            sys.executable, "-m", "pytest",
            str(self.test_dir),
            "-v" if verbose else "-q",
            "--tb=short",
            "-m", "security",
            "--junitxml=test_results_security.xml"
        ]
        
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)
        duration = time.time() - start_time
        
        self.results['security_tests'] = {
            'success': result.returncode == 0,
            'duration': duration,
            'stdout': result.stdout,
            'stderr': result.stderr
        }
        
        if result.returncode == 0:
            print(f"✅ Security tests passed in {duration:.2f}s")
        else:
            print(f"❌ Security tests failed in {duration:.2f}s")
            if verbose:
                print(result.stdout)
                print(result.stderr)
        
        return self.results['security_tests']
    
    def run_code_quality_checks(self, verbose: bool = False) -> Dict[str, Any]:
        """Run code quality checks."""
        print("📊 Running Code Quality Checks...")
        
        results = {}
        
        # Run flake8 for style checking
        print("  Running flake8...")
        flake8_cmd = [sys.executable, "-m", "flake8", "cryptoforensics", "--max-line-length=100"]
        flake8_result = subprocess.run(flake8_cmd, capture_output=True, text=True, cwd=self.project_root)
        results['flake8'] = {
            'success': flake8_result.returncode == 0,
            'output': flake8_result.stdout + flake8_result.stderr
        }
        
        # Run mypy for type checking
        print("  Running mypy...")
        mypy_cmd = [sys.executable, "-m", "mypy", "cryptoforensics", "--ignore-missing-imports"]
        mypy_result = subprocess.run(mypy_cmd, capture_output=True, text=True, cwd=self.project_root)
        results['mypy'] = {
            'success': mypy_result.returncode == 0,
            'output': mypy_result.stdout + mypy_result.stderr
        }
        
        # Run bandit for security issues
        print("  Running bandit...")
        bandit_cmd = [sys.executable, "-m", "bandit", "-r", "cryptoforensics", "-f", "json"]
        bandit_result = subprocess.run(bandit_cmd, capture_output=True, text=True, cwd=self.project_root)
        results['bandit'] = {
            'success': bandit_result.returncode == 0,
            'output': bandit_result.stdout + bandit_result.stderr
        }
        
        self.results['code_quality'] = results
        
        # Summary
        passed = sum(1 for r in results.values() if r['success'])
        total = len(results)
        
        if passed == total:
            print(f"✅ Code quality checks passed ({passed}/{total})")
        else:
            print(f"❌ Code quality checks failed ({passed}/{total})")
            if verbose:
                for tool, result in results.items():
                    if not result['success']:
                        print(f"\n{tool} issues:")
                        print(result['output'])
        
        return results
    
    def run_all_tests(self, verbose: bool = False, skip_slow: bool = False) -> Dict[str, Any]:
        """Run all test suites."""
        print("🚀 Running Complete Test Suite for CryptoForensics v3.0")
        print("=" * 60)
        
        total_start_time = time.time()
        
        # Run all test categories
        self.run_unit_tests(verbose)
        self.run_integration_tests(verbose)
        
        if not skip_slow:
            self.run_performance_tests(verbose)
        
        self.run_security_tests(verbose)
        self.run_code_quality_checks(verbose)
        
        total_duration = time.time() - total_start_time
        
        # Generate summary
        self.generate_test_summary(total_duration)
        
        return self.results
    
    def generate_test_summary(self, total_duration: float) -> None:
        """Generate and display test summary."""
        print("\n" + "=" * 60)
        print("📋 TEST SUMMARY")
        print("=" * 60)
        
        categories = [
            ('Unit Tests', 'unit_tests'),
            ('Integration Tests', 'integration_tests'),
            ('Performance Tests', 'performance_tests'),
            ('Security Tests', 'security_tests'),
            ('Code Quality', 'code_quality')
        ]
        
        passed_categories = 0
        total_categories = 0
        
        for name, key in categories:
            if key in self.results:
                total_categories += 1
                result = self.results[key]
                
                if key == 'code_quality':
                    # Special handling for code quality (multiple tools)
                    passed_tools = sum(1 for r in result.values() if r['success'])
                    total_tools = len(result)
                    status = "✅ PASSED" if passed_tools == total_tools else "❌ FAILED"
                    duration_str = ""
                    if passed_tools == total_tools:
                        passed_categories += 1
                else:
                    status = "✅ PASSED" if result['success'] else "❌ FAILED"
                    duration_str = f" ({result['duration']:.2f}s)"
                    if result['success']:
                        passed_categories += 1
                
                print(f"{name:<20} {status}{duration_str}")
        
        print("-" * 60)
        print(f"Overall Result: {passed_categories}/{total_categories} categories passed")
        print(f"Total Duration: {total_duration:.2f}s")
        
        if passed_categories == total_categories:
            print("\n🎉 ALL TESTS PASSED! CryptoForensics v3.0 is ready for deployment.")
        else:
            print(f"\n⚠️  {total_categories - passed_categories} test categories failed. Review results before deployment.")
        
        print("\n📁 Test artifacts:")
        print("  - Unit test coverage: htmlcov_unit/index.html")
        print("  - Integration test coverage: htmlcov_integration/index.html")
        print("  - Test results: test_results_*.xml")
        print("  - Performance benchmarks: benchmark_results.json")


def main():
    """Main entry point for test runner."""
    parser = argparse.ArgumentParser(description="CryptoForensics v3.0 Test Runner")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--unit", action="store_true", help="Run only unit tests")
    parser.add_argument("--integration", action="store_true", help="Run only integration tests")
    parser.add_argument("--performance", action="store_true", help="Run only performance tests")
    parser.add_argument("--security", action="store_true", help="Run only security tests")
    parser.add_argument("--quality", action="store_true", help="Run only code quality checks")
    parser.add_argument("--skip-slow", action="store_true", help="Skip slow tests")
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    # Run specific test categories if requested
    if args.unit:
        runner.run_unit_tests(args.verbose)
    elif args.integration:
        runner.run_integration_tests(args.verbose)
    elif args.performance:
        runner.run_performance_tests(args.verbose)
    elif args.security:
        runner.run_security_tests(args.verbose)
    elif args.quality:
        runner.run_code_quality_checks(args.verbose)
    else:
        # Run all tests
        runner.run_all_tests(args.verbose, args.skip_slow)
    
    # Exit with appropriate code
    if any(not result.get('success', False) for result in runner.results.values() 
           if isinstance(result, dict) and 'success' in result):
        sys.exit(1)
    else:
        sys.exit(0)


if __name__ == "__main__":
    main()
