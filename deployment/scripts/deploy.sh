#!/bin/bash

# CryptoForensics v3.0 Production Deployment Script
# Professional deployment with victim-centric investigation features

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
DEPLOYMENT_DIR="$PROJECT_ROOT/deployment"
ENV_FILE="$DEPLOYMENT_DIR/.env"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if running as root (not recommended)
    if [[ $EUID -eq 0 ]]; then
        warning "Running as root is not recommended for production deployment."
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    success "Prerequisites check passed"
}

# Setup environment
setup_environment() {
    log "Setting up environment..."
    
    # Create .env file if it doesn't exist
    if [[ ! -f "$ENV_FILE" ]]; then
        log "Creating environment file..."
        cat > "$ENV_FILE" << EOF
# CryptoForensics v3.0 Production Environment
CRYPTOFORENSICS_ENV=production

# Database Configuration
DB_PASSWORD=$(openssl rand -base64 32)

# Redis Configuration
REDIS_PASSWORD=$(openssl rand -base64 32)

# Security Keys
SECRET_KEY=$(openssl rand -base64 64)
ENCRYPTION_KEY=$(openssl rand -base64 32)

# Application Configuration
DEBUG=false
LOG_LEVEL=INFO
MAX_WORKERS=4

# Email Configuration (update with your SMTP settings)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_RETENTION_DAYS=30
BACKUP_ENCRYPTION_ENABLED=true

# Monitoring Configuration
MONITORING_ENABLED=true
METRICS_ENABLED=true
HEALTH_CHECK_ENABLED=true
EOF
        success "Environment file created at $ENV_FILE"
        warning "Please review and update the environment variables in $ENV_FILE"
    else
        log "Environment file already exists"
    fi
}

# Create necessary directories
create_directories() {
    log "Creating necessary directories..."
    
    mkdir -p "$DEPLOYMENT_DIR/data/investigations"
    mkdir -p "$DEPLOYMENT_DIR/data/evidence"
    mkdir -p "$DEPLOYMENT_DIR/data/reports"
    mkdir -p "$DEPLOYMENT_DIR/data/backups"
    mkdir -p "$DEPLOYMENT_DIR/logs"
    mkdir -p "$DEPLOYMENT_DIR/config/production"
    mkdir -p "$DEPLOYMENT_DIR/nginx/ssl"
    
    success "Directories created"
}

# Generate SSL certificates (self-signed for development)
generate_ssl_certificates() {
    log "Generating SSL certificates..."
    
    SSL_DIR="$DEPLOYMENT_DIR/nginx/ssl"
    
    if [[ ! -f "$SSL_DIR/cert.pem" ]]; then
        openssl req -x509 -newkey rsa:4096 -keyout "$SSL_DIR/key.pem" -out "$SSL_DIR/cert.pem" \
            -days 365 -nodes -subj "/C=US/ST=State/L=City/O=Organization/CN=cryptoforensics.local"
        
        success "SSL certificates generated"
        warning "Using self-signed certificates. Replace with proper certificates for production."
    else
        log "SSL certificates already exist"
    fi
}

# Build and start services
deploy_services() {
    log "Building and deploying services..."
    
    cd "$DEPLOYMENT_DIR"
    
    # Pull latest images
    docker-compose pull
    
    # Build custom images
    docker-compose build --no-cache
    
    # Start services
    docker-compose up -d
    
    success "Services deployed successfully"
}

# Wait for services to be ready
wait_for_services() {
    log "Waiting for services to be ready..."
    
    # Wait for database
    log "Waiting for database..."
    timeout=60
    while ! docker-compose exec -T postgres pg_isready -U cryptoforensics &>/dev/null; do
        sleep 2
        timeout=$((timeout - 2))
        if [[ $timeout -le 0 ]]; then
            error "Database failed to start within timeout"
            exit 1
        fi
    done
    
    # Wait for Redis
    log "Waiting for Redis..."
    timeout=60
    while ! docker-compose exec -T redis redis-cli ping &>/dev/null; do
        sleep 2
        timeout=$((timeout - 2))
        if [[ $timeout -le 0 ]]; then
            error "Redis failed to start within timeout"
            exit 1
        fi
    done
    
    # Wait for application
    log "Waiting for application..."
    timeout=120
    while ! curl -f http://localhost:8000/health &>/dev/null; do
        sleep 5
        timeout=$((timeout - 5))
        if [[ $timeout -le 0 ]]; then
            error "Application failed to start within timeout"
            exit 1
        fi
    done
    
    success "All services are ready"
}

# Run database migrations
run_migrations() {
    log "Running database migrations..."
    
    docker-compose exec cryptoforensics python -m cryptoforensics.database.migrate
    
    success "Database migrations completed"
}

# Setup monitoring
setup_monitoring() {
    log "Setting up monitoring..."
    
    # Create monitoring configuration
    cat > "$DEPLOYMENT_DIR/config/production/monitoring.yml" << EOF
monitoring:
  enabled: true
  metrics:
    enabled: true
    port: 9090
  health_checks:
    enabled: true
    interval: 30
  logging:
    level: INFO
    format: json
    file: /app/logs/application.log
  alerts:
    enabled: true
    email_notifications: true
EOF
    
    success "Monitoring configured"
}

# Display deployment information
display_deployment_info() {
    log "Deployment completed successfully!"
    echo
    echo "=== CryptoForensics v3.0 Deployment Information ==="
    echo
    echo "Application URL: https://localhost"
    echo "API Endpoint: https://localhost/api"
    echo "Health Check: http://localhost:8000/health"
    echo
    echo "Services:"
    echo "  - Application: http://localhost:8000"
    echo "  - Database: localhost:5432"
    echo "  - Redis: localhost:6379"
    echo "  - Nginx: localhost:80, localhost:443"
    echo
    echo "Data Directories:"
    echo "  - Investigations: $DEPLOYMENT_DIR/data/investigations"
    echo "  - Evidence: $DEPLOYMENT_DIR/data/evidence"
    echo "  - Reports: $DEPLOYMENT_DIR/data/reports"
    echo "  - Backups: $DEPLOYMENT_DIR/data/backups"
    echo "  - Logs: $DEPLOYMENT_DIR/logs"
    echo
    echo "Configuration:"
    echo "  - Environment: $ENV_FILE"
    echo "  - Production Config: $DEPLOYMENT_DIR/config/production/"
    echo
    echo "Management Commands:"
    echo "  - View logs: docker-compose logs -f"
    echo "  - Stop services: docker-compose down"
    echo "  - Restart services: docker-compose restart"
    echo "  - Update deployment: $0"
    echo
    warning "Please review the environment configuration in $ENV_FILE"
    warning "Update SMTP settings for email notifications"
    warning "Replace self-signed SSL certificates with proper certificates for production"
}

# Main deployment function
main() {
    log "Starting CryptoForensics v3.0 deployment..."
    
    check_prerequisites
    setup_environment
    create_directories
    generate_ssl_certificates
    deploy_services
    wait_for_services
    run_migrations
    setup_monitoring
    display_deployment_info
    
    success "Deployment completed successfully!"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "stop")
        log "Stopping services..."
        cd "$DEPLOYMENT_DIR"
        docker-compose down
        success "Services stopped"
        ;;
    "restart")
        log "Restarting services..."
        cd "$DEPLOYMENT_DIR"
        docker-compose restart
        success "Services restarted"
        ;;
    "logs")
        cd "$DEPLOYMENT_DIR"
        docker-compose logs -f
        ;;
    "status")
        cd "$DEPLOYMENT_DIR"
        docker-compose ps
        ;;
    "update")
        log "Updating deployment..."
        cd "$DEPLOYMENT_DIR"
        docker-compose pull
        docker-compose up -d --build
        success "Deployment updated"
        ;;
    *)
        echo "Usage: $0 {deploy|stop|restart|logs|status|update}"
        echo
        echo "Commands:"
        echo "  deploy  - Deploy CryptoForensics v3.0 (default)"
        echo "  stop    - Stop all services"
        echo "  restart - Restart all services"
        echo "  logs    - View service logs"
        echo "  status  - Show service status"
        echo "  update  - Update deployment"
        exit 1
        ;;
esac
