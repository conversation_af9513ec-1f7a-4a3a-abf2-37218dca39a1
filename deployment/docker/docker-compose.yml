version: '3.8'

services:
  cryptoforensics:
    build:
      context: ../../
      dockerfile: deployment/docker/Dockerfile
    container_name: cryptoforensics-app
    environment:
      - CRYPTOFORENSICS_ENV=production
      - DATABASE_URL=postgresql://cryptoforensics:${DB_PASSWORD}@postgres:5432/cryptoforensics
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
    volumes:
      - app_data:/app/data
      - app_logs:/app/logs
      - ./config/production:/app/config/production:ro
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    networks:
      - cryptoforensics-network

  postgres:
    image: postgres:15-alpine
    container_name: cryptoforensics-db
    environment:
      - POSTGRES_DB=cryptoforensics
      - POSTGRES_USER=cryptoforensics
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init/postgres:/docker-entrypoint-initdb.d:ro
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - cryptoforensics-network

  redis:
    image: redis:7-alpine
    container_name: cryptoforensics-cache
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped
    networks:
      - cryptoforensics-network

  nginx:
    image: nginx:alpine
    container_name: cryptoforensics-proxy
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - app_logs:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - cryptoforensics
    restart: unless-stopped
    networks:
      - cryptoforensics-network

  backup:
    build:
      context: ../../
      dockerfile: deployment/docker/Dockerfile.backup
    container_name: cryptoforensics-backup
    environment:
      - DATABASE_URL=postgresql://cryptoforensics:${DB_PASSWORD}@postgres:5432/cryptoforensics
      - BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
      - BACKUP_RETENTION_DAYS=30
    volumes:
      - app_data:/app/data:ro
      - backup_data:/backups
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - cryptoforensics-network

volumes:
  app_data:
    driver: local
  app_logs:
    driver: local
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backup_data:
    driver: local

networks:
  cryptoforensics-network:
    driver: bridge
