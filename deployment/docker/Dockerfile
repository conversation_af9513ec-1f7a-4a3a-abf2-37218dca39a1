# CryptoForensics v3.0 Production Dockerfile
# Professional cryptocurrency investigation platform with victim-centric features

FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV CRYPTOFORENSICS_ENV=production

# Create application user
RUN groupadd -r cryptoforensics && useradd -r -g cryptoforensics cryptoforensics

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    libssl-dev \
    libffi-dev \
    postgresql-client \
    redis-tools \
    && rm -rf /var/lib/apt/lists/*

# Set work directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt requirements-prod.txt ./

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements-prod.txt

# Copy application code
COPY cryptoforensics/ ./cryptoforensics/
COPY config/ ./config/
COPY scripts/ ./scripts/
COPY setup.py ./

# Install the application
RUN pip install -e .

# Create necessary directories
RUN mkdir -p /app/data/investigations \
    /app/data/evidence \
    /app/data/reports \
    /app/data/backups \
    /app/logs \
    /app/config/production

# Set proper permissions
RUN chown -R cryptoforensics:cryptoforensics /app
RUN chmod +x scripts/*.sh

# Switch to non-root user
USER cryptoforensics

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import cryptoforensics; print('OK')" || exit 1

# Expose port
EXPOSE 8000

# Default command
CMD ["python", "-m", "cryptoforensics.cli.interface", "--help"]
