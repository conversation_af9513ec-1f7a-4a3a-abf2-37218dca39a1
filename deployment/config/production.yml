# CryptoForensics v3.0 Production Configuration
# Professional cryptocurrency investigation platform with victim-centric features

# Application Configuration
application:
  name: "CryptoForensics"
  version: "3.0.0"
  environment: "production"
  debug: false
  
  # Server Configuration
  server:
    host: "0.0.0.0"
    port: 8000
    workers: 4
    timeout: 300
    keepalive: 2
    max_requests: 1000
    max_requests_jitter: 100

# Database Configuration
database:
  url: "${DATABASE_URL}"
  pool_size: 20
  max_overflow: 30
  pool_timeout: 30
  pool_recycle: 3600
  echo: false
  
  # Connection settings
  connect_args:
    sslmode: "require"
    connect_timeout: 10
    application_name: "cryptoforensics"

# Cache Configuration (Redis)
cache:
  url: "${REDIS_URL}"
  default_timeout: 3600
  key_prefix: "cf3:"
  
  # Connection pool settings
  connection_pool:
    max_connections: 50
    retry_on_timeout: true
    socket_timeout: 5
    socket_connect_timeout: 5

# Security Configuration
security:
  secret_key: "${SECRET_KEY}"
  encryption_key: "${ENCRYPTION_KEY}"
  
  # Password policies
  password_policy:
    min_length: 12
    require_uppercase: true
    require_lowercase: true
    require_numbers: true
    require_special_chars: true
    max_age_days: 90
  
  # Session management
  session:
    timeout_minutes: 480  # 8 hours
    secure_cookies: true
    httponly_cookies: true
    samesite: "strict"
  
  # Rate limiting
  rate_limiting:
    enabled: true
    requests_per_minute: 60
    burst_limit: 100
  
  # CORS settings
  cors:
    enabled: true
    origins: ["https://cryptoforensics.local"]
    methods: ["GET", "POST", "PUT", "DELETE"]
    headers: ["Content-Type", "Authorization"]

# Investigation Configuration
investigation:
  # Output directories
  output_directory: "/app/data"
  evidence_directory: "/app/data/evidence"
  reports_directory: "/app/data/reports"
  backups_directory: "/app/data/backups"
  
  # Processing limits
  max_concurrent_investigations: 10
  max_transaction_depth: 10
  max_addresses_per_investigation: 1000
  
  # Evidence integrity
  evidence_integrity_checks: true
  blockchain_timestamping: true
  digital_signatures: true
  
  # Victim management
  victim_management:
    enabled: true
    auto_notifications: true
    progress_updates_enabled: true
    communication_encryption: true
    gdpr_compliance: true
    data_retention_days: 2555  # 7 years

# Blockchain Configuration
blockchain:
  # Bitcoin configuration
  bitcoin:
    network: "mainnet"
    rpc_timeout: 30
    max_connections: 10
    
  # API rate limits
  api_limits:
    requests_per_second: 10
    burst_requests: 50
    backoff_factor: 2
    max_retries: 3

# Email Configuration
email:
  smtp_host: "${SMTP_HOST}"
  smtp_port: "${SMTP_PORT}"
  smtp_user: "${SMTP_USER}"
  smtp_password: "${SMTP_PASSWORD}"
  use_tls: true
  use_ssl: false
  
  # Email settings
  from_email: "<EMAIL>"
  from_name: "CryptoForensics Investigation Team"
  
  # Templates
  templates_directory: "/app/templates/email"
  default_language: "en"

# Logging Configuration
logging:
  version: 1
  disable_existing_loggers: false
  
  formatters:
    standard:
      format: "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
    json:
      format: '{"timestamp": "%(asctime)s", "level": "%(levelname)s", "logger": "%(name)s", "message": "%(message)s"}'
  
  handlers:
    console:
      class: "logging.StreamHandler"
      level: "INFO"
      formatter: "standard"
      stream: "ext://sys.stdout"
    
    file:
      class: "logging.handlers.RotatingFileHandler"
      level: "INFO"
      formatter: "json"
      filename: "/app/logs/application.log"
      maxBytes: 104857600  # 100MB
      backupCount: 10
    
    security:
      class: "logging.handlers.RotatingFileHandler"
      level: "WARNING"
      formatter: "json"
      filename: "/app/logs/security.log"
      maxBytes: 104857600  # 100MB
      backupCount: 20
  
  loggers:
    cryptoforensics:
      level: "INFO"
      handlers: ["console", "file"]
      propagate: false
    
    cryptoforensics.security:
      level: "WARNING"
      handlers: ["security"]
      propagate: false
    
    sqlalchemy.engine:
      level: "WARNING"
      handlers: ["file"]
      propagate: false
  
  root:
    level: "WARNING"
    handlers: ["console"]

# Monitoring Configuration
monitoring:
  enabled: true
  
  # Health checks
  health_checks:
    enabled: true
    endpoint: "/health"
    interval_seconds: 30
    timeout_seconds: 10
  
  # Metrics
  metrics:
    enabled: true
    endpoint: "/metrics"
    port: 9090
    
    # Custom metrics
    custom_metrics:
      - "investigation_count"
      - "evidence_items_processed"
      - "victim_communications_sent"
      - "reports_generated"
  
  # Performance monitoring
  performance:
    enabled: true
    slow_query_threshold: 1.0  # seconds
    memory_threshold_mb: 1024
    cpu_threshold_percent: 80

# Backup Configuration
backup:
  enabled: true
  
  # Schedule (cron format)
  schedule: "0 2 * * *"  # Daily at 2 AM
  
  # Retention
  retention_days: 30
  max_backup_size_gb: 100
  
  # Encryption
  encryption_enabled: true
  compression_enabled: true
  
  # Storage
  storage_path: "/app/data/backups"
  
  # What to backup
  include:
    - "investigations"
    - "evidence"
    - "reports"
    - "database"
    - "configuration"
  
  exclude:
    - "*.tmp"
    - "*.log"
    - "cache/*"

# Performance Configuration
performance:
  # Connection pooling
  connection_pooling:
    enabled: true
    pool_size: 20
    max_overflow: 30
  
  # Caching
  caching:
    enabled: true
    default_timeout: 3600
    cache_size_mb: 512
  
  # Async processing
  async_processing:
    enabled: true
    max_workers: 4
    queue_size: 1000
  
  # Resource limits
  resource_limits:
    max_memory_mb: 2048
    max_cpu_percent: 80
    max_disk_usage_percent: 85

# Compliance Configuration
compliance:
  # GDPR
  gdpr:
    enabled: true
    data_retention_days: 2555  # 7 years
    consent_required: true
    right_to_be_forgotten: true
    data_portability: true
  
  # Audit trail
  audit_trail:
    enabled: true
    log_all_actions: true
    retention_days: 3650  # 10 years
    tamper_protection: true
  
  # Legal requirements
  legal:
    chain_of_custody: true
    digital_signatures: true
    evidence_integrity: true
    expert_witness_ready: true

# Feature Flags
features:
  victim_management: true
  automated_reporting: true
  blockchain_timestamping: true
  advanced_analytics: true
  law_enforcement_integration: true
  victim_communication: true
  evidence_packaging: true
  compliance_monitoring: true
