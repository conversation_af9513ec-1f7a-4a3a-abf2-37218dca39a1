#!/usr/bin/env python3
"""
CryptoForensics - Professional Cryptocurrency Investigation Tool
Main entry point for the cryptocurrency forensics investigation tool.

This script provides a unified interface to both the legacy standalone tool
and the modern modular framework.

Usage:
    python main.py --help                    # Show help
    python main.py legacy --help             # Use legacy tool
    python main.py investigate --help        # Use modular framework
    python main.py --version                 # Show version info
"""

import sys
import argparse
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_logging(verbose: bool = False):
    """Set up logging configuration."""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('crypto_investigation.log')
        ]
    )

def print_banner():
    """Print the application banner."""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    🔍 CryptoForensics Investigation Suite                     ║
║                          Professional Cryptocurrency Forensics                ║
║                                                                              ║
║  Features:                                                                   ║
║  • Bitcoin transaction tracing and analysis                                 ║
║  • Advanced pattern recognition and suspicious activity detection           ║
║  • Professional evidence collection and chain of custody                    ║
║  • Interactive visualizations and comprehensive reporting                    ║
║  • Victim-centric investigation management                                  ║
║  • Law enforcement integration and compliance                               ║
║                                                                              ║
║  Version: 3.0.0 | License: MIT | Professional Grade                        ║
╚══════════════════════════════════════════════════════════════════════════════╝
"""
    print(banner)

def run_legacy_tool(remaining_args):
    """Run the legacy crypto investigator tool."""
    try:
        # Import and run legacy tool
        legacy_path = project_root / "tools" / "legacy_crypto_investigator.py"
        if not legacy_path.exists():
            print("❌ Legacy tool not found. Please ensure tools/legacy_crypto_investigator.py exists.")
            return 1

        print("🔄 Starting legacy Bitcoin forensics investigator...")

        # Execute legacy tool with remaining arguments
        import subprocess
        cmd = [sys.executable, str(legacy_path)] + remaining_args
        result = subprocess.run(cmd, cwd=project_root)
        return result.returncode

    except Exception as e:
        print(f"❌ Error running legacy tool: {e}")
        return 1

def run_modular_framework(remaining_args):
    """Run the modular cryptoforensics framework."""
    try:
        # Check if dependencies are available
        try:
            import cryptoforensics
            print(f"✅ CryptoForensics v{cryptoforensics.__version__} loaded successfully")
        except ImportError as e:
            print(f"❌ CryptoForensics framework not available: {e}")
            print("💡 Try installing missing dependencies: pip install -r requirements.txt")
            print("💡 Or use the legacy tool: python main.py legacy --help")
            return 1

        # Import and run CLI with arguments
        from cryptoforensics.cli.interface import main as cli_main
        print("🔄 Starting CryptoForensics modular framework...")

        # Temporarily modify sys.argv for the CLI
        original_argv = sys.argv[:]
        sys.argv = ['cryptoforensics'] + remaining_args
        try:
            cli_main()
            return 0
        finally:
            sys.argv = original_argv

    except Exception as e:
        print(f"❌ Error running modular framework: {e}")
        return 1

def show_version():
    """Show version information."""
    print("CryptoForensics Investigation Suite")
    print("Version: 3.0.0")
    print("License: MIT")
    print("Author: Cryptocurrency Investigation Team")
    print()

    # Try to show framework version
    try:
        import cryptoforensics
        print(f"Modular Framework: v{cryptoforensics.__version__} (Available)")
    except ImportError:
        print("Modular Framework: Not available (missing dependencies)")

    # Check legacy tool
    legacy_path = project_root / "tools" / "legacy_crypto_investigator.py"
    if legacy_path.exists():
        print("Legacy Tool: v2.0.0 (Available)")
    else:
        print("Legacy Tool: Not found")

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="CryptoForensics - Professional Cryptocurrency Investigation Tool",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py --version                           # Show version info
  python main.py legacy --interactive                # Run legacy tool interactively
  python main.py legacy --txid abc123... --address 1A1zP1eP...
  python main.py investigate --help                  # Show modular framework help
  python main.py investigate trace --txid abc123...  # Use modular framework
        """
    )

    parser.add_argument('--version', action='store_true', help='Show version information')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose logging')

    subparsers = parser.add_subparsers(dest='command', help='Available commands')

    # Legacy tool subcommand
    legacy_parser = subparsers.add_parser('legacy', help='Use legacy Bitcoin forensics tool',
                                         add_help=False)  # Disable help to pass through

    # Modular framework subcommand
    investigate_parser = subparsers.add_parser('investigate', help='Use modular CryptoForensics framework',
                                             add_help=False)  # Disable help to pass through

    # Parse known args to handle legacy/investigate commands properly
    if len(sys.argv) > 1 and sys.argv[1] in ['legacy', 'investigate']:
        command = sys.argv[1]
        remaining_args = sys.argv[2:]

        # Set up basic logging
        setup_logging('--verbose' in remaining_args or '-v' in remaining_args)

        # Route to appropriate tool
        if command == 'legacy':
            return run_legacy_tool(remaining_args)
        elif command == 'investigate':
            return run_modular_framework(remaining_args)

    # Parse normally for other cases
    args = parser.parse_args()

    # Set up logging
    setup_logging(args.verbose)

    # Handle version request
    if args.version:
        show_version()
        return 0

    # Show banner for interactive use
    print_banner()
    parser.print_help()
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⚠️  Investigation interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ An unexpected error occurred: {e}")
        sys.exit(1)
