# Core dependencies
requests>=2.31.0
networkx>=3.1
plotly>=5.15.0
tqdm>=4.65.0
pydantic>=2.0.0
click>=8.1.0
rich>=13.0.0
aiohttp>=3.8.0
asyncio-throttle>=1.0.2

# Data processing and analysis
pandas>=2.0.0
numpy>=1.24.0
scipy>=1.10.0
scikit-learn>=1.3.0

# Cryptography and security
cryptography>=41.0.0
hashlib-compat>=1.0.0

# Configuration and serialization
pyyaml>=6.0
toml>=0.10.2
python-dotenv>=1.0.0

# Logging and monitoring
structlog>=23.0.0
prometheus-client>=0.17.0

# Database support (optional)
sqlalchemy>=2.0.0
alembic>=1.11.0

# Visualization enhancements
matplotlib>=3.7.0
seaborn>=0.12.0
graphviz>=0.20.0

# API and web support
fastapi>=0.100.0
uvicorn>=0.23.0
httpx>=0.24.0

# Testing and development
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-asyncio>=0.21.0
pytest-mock>=3.11.0

# Code quality
black>=23.7.0
flake8>=6.0.0
mypy>=1.5.0
isort>=5.12.0
pre-commit>=3.3.0

# Documentation
sphinx>=7.1.0
sphinx-rtd-theme>=1.3.0
myst-parser>=2.0.0

# Performance monitoring
memory-profiler>=0.61.0
line-profiler>=4.1.0
psutil>=5.9.0
