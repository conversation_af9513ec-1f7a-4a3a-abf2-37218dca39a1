"""
Communication module for CryptoForensics v3.0

Provides secure victim communication features including automated notifications,
professional templates, multi-channel delivery, and progress tracking for
victim-centric investigations.
"""

from .victim_communication import (
    VictimCommunicationManager, CommunicationMessage, CommunicationTemplate,
    CommunicationType, DeliveryMethod, NotificationPriority
)

__all__ = [
    # Victim communication system
    "VictimCommunicationManager",
    "CommunicationMessage", 
    "CommunicationTemplate",
    "CommunicationType",
    "DeliveryMethod",
    "NotificationPriority"
]
