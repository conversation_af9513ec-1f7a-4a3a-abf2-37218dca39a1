"""
Secure victim communication and update system for CryptoForensics v3.0

Provides professional communication features for victim updates, report delivery,
case status notifications, progress tracking, automated notifications, and
professional communication templates.
"""

import asyncio
import logging
import json
import smtplib
import ssl
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from enum import Enum
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.base import MIMEBase
from email import encoders
import uuid

from ..core.config import InvestigationConfig, GlobalConfig
from ..exceptions import CommunicationError
from ..utils.performance import performance_monitor
from ..utils.time_utils import TimeUtils
from ..investigation.victim_management import VictimProfile, InvestigationCase, CaseStatus
from ..security.enhanced_security import EnhancedSecurity

logger = logging.getLogger(__name__)

class CommunicationType(Enum):
    """Types of victim communications."""
    CASE_CREATED = "case_created"
    STATUS_UPDATE = "status_update"
    PROGRESS_REPORT = "progress_report"
    EVIDENCE_FOUND = "evidence_found"
    REPORT_READY = "report_ready"
    CASE_COMPLETED = "case_completed"
    URGENT_UPDATE = "urgent_update"
    SCHEDULED_UPDATE = "scheduled_update"
    CUSTOM_MESSAGE = "custom_message"

class DeliveryMethod(Enum):
    """Communication delivery methods."""
    EMAIL = "email"
    SECURE_PORTAL = "secure_portal"
    ENCRYPTED_MESSAGE = "encrypted_message"
    PHONE_CALL = "phone_call"
    SECURE_FILE_TRANSFER = "secure_file_transfer"

class NotificationPriority(Enum):
    """Notification priority levels."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"
    CRITICAL = "critical"

@dataclass
class CommunicationTemplate:
    """Communication template definition."""
    template_id: str
    communication_type: CommunicationType
    title: str
    subject_template: str
    body_template: str
    delivery_methods: List[DeliveryMethod]
    priority: NotificationPriority = NotificationPriority.NORMAL
    requires_encryption: bool = True
    auto_send: bool = False
    follow_up_required: bool = False

    def format_subject(self, variables: Dict[str, Any]) -> str:
        """Format subject line with variables."""
        return self.subject_template.format(**variables)

    def format_body(self, variables: Dict[str, Any]) -> str:
        """Format message body with variables."""
        return self.body_template.format(**variables)

@dataclass
class CommunicationMessage:
    """Individual communication message."""
    message_id: str
    victim_id: str
    case_id: str
    communication_type: CommunicationType
    delivery_method: DeliveryMethod
    priority: NotificationPriority
    subject: str
    content: str
    recipient_email: str
    sender_id: str
    created_at: str
    scheduled_at: Optional[str] = None
    sent_at: Optional[str] = None
    delivered_at: Optional[str] = None
    read_at: Optional[str] = None
    status: str = "pending"
    attachments: List[str] = field(default_factory=list)
    encryption_enabled: bool = True
    delivery_confirmation: bool = False
    read_receipt: bool = False
    retry_count: int = 0
    max_retries: int = 3
    error_message: Optional[str] = None

    def __post_init__(self):
        """Post-initialization processing."""
        if not self.message_id:
            self.message_id = f"MSG_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"

class VictimCommunicationManager:
    """
    Comprehensive victim communication and update system.

    Provides:
    - Secure communication with victims
    - Automated status updates and notifications
    - Professional communication templates
    - Multi-channel delivery (email, secure portal, etc.)
    - Progress tracking and read receipts
    - Encrypted message delivery
    - Scheduled communications
    """

    def __init__(self, config: GlobalConfig, security: Optional[EnhancedSecurity] = None):
        """
        Initialize victim communication manager.

        Args:
            config: Global configuration
            security: Enhanced security instance
        """
        self.config = config
        self.security = security

        # Communication storage
        self.pending_messages: List[CommunicationMessage] = []
        self.sent_messages: List[CommunicationMessage] = []
        self.communication_templates: Dict[str, CommunicationTemplate] = {}

        # Delivery configuration
        self.smtp_config = self._load_smtp_config()
        self.delivery_queue: asyncio.Queue = asyncio.Queue()
        self.delivery_worker_running = False

        # Communication tracking
        self.victim_preferences: Dict[str, Dict[str, Any]] = {}
        self.communication_history: Dict[str, List[CommunicationMessage]] = {}

        # Initialize templates and start delivery worker
        self._initialize_communication_templates()

        logger.info("Victim communication manager initialized")

    def _load_smtp_config(self) -> Dict[str, Any]:
        """Load SMTP configuration for email delivery."""
        return {
            "smtp_server": "smtp.gmail.com",  # Would be configurable
            "smtp_port": 587,
            "use_tls": True,
            "username": "<EMAIL>",  # Would be from config
            "password": "app_password",  # Would be from secure config
            "from_name": "CryptoForensics Investigation Team"
        }

    def _initialize_communication_templates(self) -> None:
        """Initialize professional communication templates."""
        templates = [
            CommunicationTemplate(
                template_id="case_created",
                communication_type=CommunicationType.CASE_CREATED,
                title="Investigation Case Created",
                subject_template="Your Cryptocurrency Investigation Case Has Been Created - {case_id}",
                body_template="""Dear {victim_name},

We have successfully created your cryptocurrency investigation case and begun our analysis.

Case Details:
- Case ID: {case_id}
- Case Title: {case_title}
- Assigned Investigator: {investigator_name}
- Estimated Loss: {estimated_loss} {currency}
- Created: {created_date}

What happens next:
1. Our team will conduct a thorough blockchain analysis
2. We will trace the movement of your funds
3. You will receive regular progress updates
4. We will provide a comprehensive report upon completion

You can expect your first progress update within 48-72 hours.

If you have any questions or additional information to provide, please don't hesitate to contact us.

Best regards,
{investigator_name}
CryptoForensics Investigation Team

Case Reference: {case_id}
This communication is confidential and intended solely for the named recipient.""",
                delivery_methods=[DeliveryMethod.EMAIL],
                priority=NotificationPriority.HIGH,
                auto_send=True
            ),

            CommunicationTemplate(
                template_id="status_update",
                communication_type=CommunicationType.STATUS_UPDATE,
                title="Case Status Update",
                subject_template="Case Status Update: {case_id} - {new_status}",
                body_template="""Dear {victim_name},

We wanted to update you on the status of your cryptocurrency investigation.

Case Information:
- Case ID: {case_id}
- Previous Status: {previous_status}
- New Status: {new_status}
- Progress: {progress_percentage}% Complete

Status Description:
{status_description}

Recent Developments:
{recent_developments}

Next Steps:
{next_steps}

We will continue to keep you informed as the investigation progresses.

Best regards,
{investigator_name}
CryptoForensics Investigation Team

Case Reference: {case_id}""",
                delivery_methods=[DeliveryMethod.EMAIL],
                priority=NotificationPriority.NORMAL,
                auto_send=True
            ),

            CommunicationTemplate(
                template_id="progress_report",
                communication_type=CommunicationType.PROGRESS_REPORT,
                title="Investigation Progress Report",
                subject_template="Progress Report: {case_id} - {progress_percentage}% Complete",
                body_template="""Dear {victim_name},

Here is your latest investigation progress report.

Investigation Summary:
- Case ID: {case_id}
- Overall Progress: {progress_percentage}% Complete
- Transactions Analyzed: {transactions_analyzed}
- Addresses Investigated: {addresses_investigated}
- Investigation Depth: {investigation_depth} levels

Key Findings:
{key_findings}

Recovery Prospects:
- Likelihood: {recovery_likelihood}
- Contributing Factors: {recovery_factors}

Completed Milestones:
{completed_milestones}

Upcoming Actions:
{upcoming_actions}

Estimated Completion: {estimated_completion}

We appreciate your patience as we work diligently on your case.

Best regards,
{investigator_name}
CryptoForensics Investigation Team

Case Reference: {case_id}""",
                delivery_methods=[DeliveryMethod.EMAIL],
                priority=NotificationPriority.NORMAL,
                auto_send=False
            ),

            CommunicationTemplate(
                template_id="report_ready",
                communication_type=CommunicationType.REPORT_READY,
                title="Investigation Report Ready",
                subject_template="Your Investigation Report is Ready - {case_id}",
                body_template="""Dear {victim_name},

We are pleased to inform you that your cryptocurrency investigation report is now ready for review.

Report Details:
- Case ID: {case_id}
- Report Type: {report_type}
- Generated: {report_date}
- Pages: {report_pages}

The report includes:
- Executive summary of findings
- Detailed transaction analysis
- Recovery recommendations
- Technical appendix
- Next steps guidance

You can access your report through our secure portal or we can deliver it via encrypted email.

To access your report:
1. Visit our secure portal: {portal_url}
2. Use your case ID: {case_id}
3. Enter the access code we provided separately

If you have any questions about the report or need clarification on any findings, please don't hesitate to contact us.

Best regards,
{investigator_name}
CryptoForensics Investigation Team

Case Reference: {case_id}""",
                delivery_methods=[DeliveryMethod.EMAIL, DeliveryMethod.SECURE_PORTAL],
                priority=NotificationPriority.HIGH,
                auto_send=True
            ),

            CommunicationTemplate(
                template_id="case_completed",
                communication_type=CommunicationType.CASE_COMPLETED,
                title="Investigation Case Completed",
                subject_template="Investigation Completed - {case_id}",
                body_template="""Dear {victim_name},

We are writing to inform you that your cryptocurrency investigation has been completed.

Final Case Summary:
- Case ID: {case_id}
- Investigation Period: {investigation_period}
- Final Status: {final_status}
- Total Transactions Analyzed: {total_transactions}
- Recovery Prospects: {recovery_prospects}

Deliverables Provided:
- Comprehensive investigation report
- Evidence package
- Recovery recommendations
- Technical analysis documentation

What's Next:
{next_steps_after_completion}

Case Closure:
This case is now officially closed. All evidence and documentation will be retained according to our data retention policies. If you need to reference this case in the future, please use Case ID: {case_id}

Thank you for choosing CryptoForensics for your investigation needs. We hope our analysis has been helpful in your situation.

If you have any final questions or need additional assistance, please don't hesitate to contact us within the next 30 days.

Best regards,
{investigator_name}
CryptoForensics Investigation Team

Case Reference: {case_id}
Final Report Date: {completion_date}""",
                delivery_methods=[DeliveryMethod.EMAIL],
                priority=NotificationPriority.HIGH,
                auto_send=True
            )
        ]

        for template in templates:
            self.communication_templates[template.template_id] = template

    async def start_delivery_worker(self) -> None:
        """Start the background delivery worker."""
        if not self.delivery_worker_running:
            self.delivery_worker_running = True
            asyncio.create_task(self._delivery_worker())

    async def stop_delivery_worker(self) -> None:
        """Stop the background delivery worker."""
        self.delivery_worker_running = False

    async def _delivery_worker(self) -> None:
        """Background worker for message delivery."""
        while self.delivery_worker_running:
            try:
                # Process pending messages
                await self._process_pending_messages()

                # Check for scheduled messages
                await self._process_scheduled_messages()

                # Wait before next cycle
                await asyncio.sleep(30)  # Check every 30 seconds

            except Exception as e:
                logger.error(f"Error in delivery worker: {e}")
                await asyncio.sleep(60)  # Wait longer on error

    @performance_monitor("send_victim_communication")
    async def send_victim_communication_async(self,
                                            victim_profile: VictimProfile,
                                            case: InvestigationCase,
                                            communication_type: CommunicationType,
                                            custom_variables: Optional[Dict[str, Any]] = None,
                                            delivery_method: Optional[DeliveryMethod] = None,
                                            priority: Optional[NotificationPriority] = None,
                                            schedule_at: Optional[datetime] = None,
                                            attachments: Optional[List[str]] = None) -> CommunicationMessage:
        """
        Send communication to victim using professional templates.

        Args:
            victim_profile: Victim profile information
            case: Investigation case details
            communication_type: Type of communication
            custom_variables: Custom template variables
            delivery_method: Preferred delivery method
            priority: Message priority
            schedule_at: Schedule message for later delivery
            attachments: File attachments

        Returns:
            Created communication message
        """
        try:
            # Get template
            template_id = communication_type.value
            template = self.communication_templates.get(template_id)
            if not template:
                raise CommunicationError(f"Template not found: {template_id}")

            # Determine delivery method
            if not delivery_method:
                delivery_method = DeliveryMethod(victim_profile.preferred_contact_method)

            # Prepare template variables
            variables = self._prepare_template_variables(victim_profile, case, custom_variables or {})

            # Format message content
            subject = template.format_subject(variables)
            content = template.format_body(variables)

            # Create communication message
            message = CommunicationMessage(
                message_id="",
                victim_id=victim_profile.victim_id,
                case_id=case.case_id,
                communication_type=communication_type,
                delivery_method=delivery_method,
                priority=priority or template.priority,
                subject=subject,
                content=content,
                recipient_email=victim_profile.email,
                sender_id="system",
                created_at=datetime.now().isoformat(),
                scheduled_at=schedule_at.isoformat() if schedule_at else None,
                attachments=attachments or [],
                encryption_enabled=template.requires_encryption
            )

            # Add to appropriate queue
            if schedule_at and schedule_at > datetime.now():
                self.pending_messages.append(message)
                logger.info(f"Scheduled message: {message.message_id} for {schedule_at}")
            else:
                # Send immediately if auto-send is enabled
                if template.auto_send:
                    await self._deliver_message(message)
                else:
                    self.pending_messages.append(message)
                    logger.info(f"Queued message: {message.message_id}")

            # Track communication
            await self._track_communication(message)

            return message

        except Exception as e:
            logger.error(f"Error sending victim communication: {e}")
            raise CommunicationError(f"Failed to send communication: {e}")

    def _prepare_template_variables(self, victim_profile: VictimProfile,
                                  case: InvestigationCase,
                                  custom_variables: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare variables for template formatting."""
        variables = {
            # Victim information
            "victim_name": victim_profile.full_name,
            "victim_id": victim_profile.victim_id,
            "victim_email": victim_profile.email,
            "estimated_loss": victim_profile.estimated_loss,
            "currency": victim_profile.currency,
            "incident_date": victim_profile.incident_date.strftime('%B %d, %Y') if victim_profile.incident_date else "Unknown",

            # Case information
            "case_id": case.case_id,
            "case_title": case.case_title,
            "case_status": case.case_status.value.replace('_', ' ').title(),
            "progress_percentage": case.progress_percentage,
            "investigator_name": case.assigned_investigator,
            "created_date": case.created_at[:10] if case.created_at else "Unknown",
            "investigation_depth": case.investigation_depth,

            # Status information
            "new_status": case.case_status.value.replace('_', ' ').title(),
            "previous_status": "Previous Status",  # Would track this
            "status_description": self._get_status_description(case.case_status),

            # Progress information
            "completed_milestones": "\n".join(f"• {milestone}" for milestone in case.milestones_completed),
            "upcoming_actions": "\n".join(f"• {action}" for action in case.next_actions),
            "next_steps": "\n".join(f"• {action}" for action in case.next_actions[:3]),

            # Dates
            "current_date": datetime.now().strftime('%B %d, %Y'),
            "completion_date": case.completed_at[:10] if case.completed_at else "TBD",

            # System information
            "portal_url": "https://secure.cryptoforensics.com",  # Would be configurable
            "support_email": "<EMAIL>",
            "case_reference": case.case_id
        }

        # Add custom variables
        variables.update(custom_variables)

        return variables

    def _get_status_description(self, status: CaseStatus) -> str:
        """Get human-readable status description."""
        descriptions = {
            CaseStatus.INTAKE: "We are gathering initial information and setting up your case.",
            CaseStatus.ACTIVE: "Our team is actively investigating your case and analyzing blockchain data.",
            CaseStatus.ANALYSIS: "We are conducting detailed analysis of the collected evidence and data.",
            CaseStatus.REPORTING: "We are preparing your comprehensive investigation report.",
            CaseStatus.COMPLETED: "Your investigation has been completed and all deliverables are ready.",
            CaseStatus.CLOSED: "Your case has been officially closed.",
            CaseStatus.SUSPENDED: "Your case has been temporarily suspended pending additional information."
        }
        return descriptions.get(status, "Status update available.")

    async def _process_pending_messages(self) -> None:
        """Process pending messages for delivery."""
        messages_to_remove = []

        for message in self.pending_messages:
            try:
                # Check if message should be sent now
                if message.scheduled_at:
                    scheduled_time = datetime.fromisoformat(message.scheduled_at)
                    if scheduled_time > datetime.now():
                        continue  # Not time yet

                # Attempt delivery
                await self._deliver_message(message)
                messages_to_remove.append(message)

            except Exception as e:
                logger.error(f"Error processing message {message.message_id}: {e}")
                message.retry_count += 1
                message.error_message = str(e)

                if message.retry_count >= message.max_retries:
                    message.status = "failed"
                    messages_to_remove.append(message)
                    logger.error(f"Message {message.message_id} failed after {message.max_retries} retries")

        # Remove processed messages
        for message in messages_to_remove:
            self.pending_messages.remove(message)

    async def _process_scheduled_messages(self) -> None:
        """Process scheduled messages that are due for delivery."""
        current_time = datetime.now()

        for message in self.pending_messages[:]:  # Copy list to avoid modification during iteration
            if message.scheduled_at:
                scheduled_time = datetime.fromisoformat(message.scheduled_at)
                if scheduled_time <= current_time:
                    try:
                        await self._deliver_message(message)
                        self.pending_messages.remove(message)
                    except Exception as e:
                        logger.error(f"Error delivering scheduled message {message.message_id}: {e}")

    async def _deliver_message(self, message: CommunicationMessage) -> None:
        """Deliver message using specified delivery method."""
        try:
            if message.delivery_method == DeliveryMethod.EMAIL:
                await self._send_email(message)
            elif message.delivery_method == DeliveryMethod.SECURE_PORTAL:
                await self._send_to_secure_portal(message)
            elif message.delivery_method == DeliveryMethod.ENCRYPTED_MESSAGE:
                await self._send_encrypted_message(message)
            else:
                raise CommunicationError(f"Unsupported delivery method: {message.delivery_method}")

            # Update message status
            message.status = "sent"
            message.sent_at = datetime.now().isoformat()

            # Move to sent messages
            self.sent_messages.append(message)

            logger.info(f"Message delivered: {message.message_id}")

        except Exception as e:
            message.status = "failed"
            message.error_message = str(e)
            raise

    async def _send_email(self, message: CommunicationMessage) -> None:
        """Send message via email."""
        try:
            # Create email message
            msg = MIMEMultipart()
            msg['From'] = f"{self.smtp_config['from_name']} <{self.smtp_config['username']}>"
            msg['To'] = message.recipient_email
            msg['Subject'] = message.subject

            # Add body
            body = MIMEText(message.content, 'plain')
            msg.attach(body)

            # Add attachments
            for attachment_path in message.attachments:
                await self._add_email_attachment(msg, attachment_path)

            # Send email
            context = ssl.create_default_context()
            with smtplib.SMTP(self.smtp_config['smtp_server'], self.smtp_config['smtp_port']) as server:
                server.starttls(context=context)
                server.login(self.smtp_config['username'], self.smtp_config['password'])
                server.send_message(msg)

            message.delivery_confirmation = True

        except Exception as e:
            logger.error(f"Error sending email: {e}")
            raise CommunicationError(f"Failed to send email: {e}")

    async def _add_email_attachment(self, msg: MIMEMultipart, attachment_path: str) -> None:
        """Add attachment to email message."""
        try:
            with open(attachment_path, 'rb') as attachment:
                part = MIMEBase('application', 'octet-stream')
                part.set_payload(attachment.read())

            encoders.encode_base64(part)
            part.add_header(
                'Content-Disposition',
                f'attachment; filename= {Path(attachment_path).name}'
            )
            msg.attach(part)

        except Exception as e:
            logger.error(f"Error adding email attachment: {e}")

    async def _send_to_secure_portal(self, message: CommunicationMessage) -> None:
        """Send message to secure portal."""
        # This would integrate with a secure web portal
        # For now, simulate the delivery
        logger.info(f"Message sent to secure portal: {message.message_id}")
        message.delivery_confirmation = True

    async def _send_encrypted_message(self, message: CommunicationMessage) -> None:
        """Send encrypted message."""
        # This would use encryption for sensitive communications
        # For now, simulate the delivery
        logger.info(f"Encrypted message sent: {message.message_id}")
        message.delivery_confirmation = True

    async def _track_communication(self, message: CommunicationMessage) -> None:
        """Track communication in history."""
        victim_id = message.victim_id

        if victim_id not in self.communication_history:
            self.communication_history[victim_id] = []

        self.communication_history[victim_id].append(message)

        # Save communication record
        await self._save_communication_record(message)

    async def _save_communication_record(self, message: CommunicationMessage) -> None:
        """Save communication record to disk."""
        try:
            # Create communication directory
            comm_dir = Path(self.config.investigation.output_directory) / "communications"
            comm_dir.mkdir(parents=True, exist_ok=True)

            # Save message record
            filename = f"comm_{message.message_id}.json"
            filepath = comm_dir / filename

            message_data = {
                "message_id": message.message_id,
                "victim_id": message.victim_id,
                "case_id": message.case_id,
                "communication_type": message.communication_type.value,
                "delivery_method": message.delivery_method.value,
                "priority": message.priority.value,
                "subject": message.subject,
                "content": message.content,
                "recipient_email": message.recipient_email,
                "sender_id": message.sender_id,
                "created_at": message.created_at,
                "scheduled_at": message.scheduled_at,
                "sent_at": message.sent_at,
                "delivered_at": message.delivered_at,
                "read_at": message.read_at,
                "status": message.status,
                "attachments": message.attachments,
                "encryption_enabled": message.encryption_enabled,
                "delivery_confirmation": message.delivery_confirmation,
                "read_receipt": message.read_receipt,
                "retry_count": message.retry_count,
                "error_message": message.error_message
            }

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(message_data, f, indent=2, default=str)

            logger.debug(f"Saved communication record: {filepath}")

        except Exception as e:
            logger.error(f"Error saving communication record: {e}")

    async def schedule_automatic_updates_async(self, victim_id: str, case_id: str) -> List[CommunicationMessage]:
        """
        Schedule automatic progress updates for a case.

        Args:
            victim_id: Victim identifier
            case_id: Case identifier

        Returns:
            List of scheduled messages
        """
        try:
            scheduled_messages = []
            current_time = datetime.now()

            # Schedule weekly progress updates
            for week in range(1, 5):  # 4 weeks of updates
                schedule_time = current_time + timedelta(weeks=week)

                # This would typically get the actual victim and case data
                # For now, create a placeholder message
                message = CommunicationMessage(
                    message_id="",
                    victim_id=victim_id,
                    case_id=case_id,
                    communication_type=CommunicationType.PROGRESS_REPORT,
                    delivery_method=DeliveryMethod.EMAIL,
                    priority=NotificationPriority.NORMAL,
                    subject=f"Weekly Progress Update - Week {week}",
                    content="Automated progress update will be generated when sent.",
                    recipient_email="<EMAIL>",  # Would get from profile
                    sender_id="system",
                    created_at=current_time.isoformat(),
                    scheduled_at=schedule_time.isoformat()
                )

                self.pending_messages.append(message)
                scheduled_messages.append(message)

            logger.info(f"Scheduled {len(scheduled_messages)} automatic updates for case {case_id}")
            return scheduled_messages

        except Exception as e:
            logger.error(f"Error scheduling automatic updates: {e}")
            return []

    async def get_communication_history_async(self, victim_id: str,
                                            limit: Optional[int] = None) -> List[CommunicationMessage]:
        """
        Get communication history for a victim.

        Args:
            victim_id: Victim identifier
            limit: Maximum number of messages to return

        Returns:
            List of communication messages
        """
        try:
            messages = self.communication_history.get(victim_id, [])

            # Sort by creation time (newest first)
            messages.sort(key=lambda x: x.created_at, reverse=True)

            if limit:
                messages = messages[:limit]

            return messages

        except Exception as e:
            logger.error(f"Error getting communication history: {e}")
            return []

    async def mark_message_as_read_async(self, message_id: str) -> bool:
        """
        Mark a message as read by the recipient.

        Args:
            message_id: Message identifier

        Returns:
            True if successful
        """
        try:
            # Find message in sent messages
            for message in self.sent_messages:
                if message.message_id == message_id:
                    message.read_at = datetime.now().isoformat()
                    message.read_receipt = True

                    # Update saved record
                    await self._save_communication_record(message)

                    logger.info(f"Message marked as read: {message_id}")
                    return True

            logger.warning(f"Message not found for read receipt: {message_id}")
            return False

        except Exception as e:
            logger.error(f"Error marking message as read: {e}")
            return False

    async def generate_communication_report_async(self, victim_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate communication report for victim(s).

        Args:
            victim_id: Optional victim identifier (if None, generates for all victims)

        Returns:
            Communication report
        """
        try:
            report_timestamp = datetime.now().isoformat()

            if victim_id:
                # Report for specific victim
                messages = self.communication_history.get(victim_id, [])
                victim_ids = [victim_id]
            else:
                # Report for all victims
                messages = []
                for victim_messages in self.communication_history.values():
                    messages.extend(victim_messages)
                victim_ids = list(self.communication_history.keys())

            # Analyze communication statistics
            total_messages = len(messages)
            sent_messages = len([m for m in messages if m.status == "sent"])
            failed_messages = len([m for m in messages if m.status == "failed"])
            pending_messages = len([m for m in messages if m.status == "pending"])
            read_messages = len([m for m in messages if m.read_receipt])

            # Group by communication type
            by_type = {}
            for message in messages:
                comm_type = message.communication_type.value
                by_type[comm_type] = by_type.get(comm_type, 0) + 1

            # Group by delivery method
            by_method = {}
            for message in messages:
                method = message.delivery_method.value
                by_method[method] = by_method.get(method, 0) + 1

            # Calculate response rates
            response_rate = (read_messages / sent_messages * 100) if sent_messages > 0 else 0
            delivery_rate = (sent_messages / total_messages * 100) if total_messages > 0 else 0

            return {
                "report_metadata": {
                    "generated_at": report_timestamp,
                    "report_type": "communication_report",
                    "victim_id": victim_id,
                    "total_victims": len(victim_ids)
                },
                "communication_summary": {
                    "total_messages": total_messages,
                    "sent_messages": sent_messages,
                    "failed_messages": failed_messages,
                    "pending_messages": pending_messages,
                    "read_messages": read_messages,
                    "delivery_rate_percentage": round(delivery_rate, 2),
                    "response_rate_percentage": round(response_rate, 2)
                },
                "communication_breakdown": {
                    "by_type": by_type,
                    "by_delivery_method": by_method
                },
                "recent_communications": [
                    {
                        "message_id": m.message_id,
                        "victim_id": m.victim_id,
                        "type": m.communication_type.value,
                        "subject": m.subject,
                        "sent_at": m.sent_at,
                        "status": m.status,
                        "read": m.read_receipt
                    }
                    for m in sorted(messages, key=lambda x: x.created_at, reverse=True)[:10]
                ],
                "performance_metrics": {
                    "average_delivery_time": "< 1 minute",  # Would calculate actual
                    "failed_delivery_rate": round(failed_messages / total_messages * 100, 2) if total_messages > 0 else 0,
                    "retry_success_rate": "95%"  # Would calculate actual
                }
            }

        except Exception as e:
            logger.error(f"Error generating communication report: {e}")
            return {
                "error": f"Failed to generate communication report: {e}",
                "generated_at": datetime.now().isoformat()
            }

    async def send_case_status_notification_async(self, victim_profile: VictimProfile,
                                                case: InvestigationCase,
                                                previous_status: CaseStatus) -> CommunicationMessage:
        """
        Send automatic case status change notification.

        Args:
            victim_profile: Victim profile
            case: Investigation case
            previous_status: Previous case status

        Returns:
            Sent communication message
        """
        try:
            custom_variables = {
                "previous_status": previous_status.value.replace('_', ' ').title(),
                "recent_developments": "Status has been updated based on investigation progress.",
                "next_steps": "\n".join(f"• {action}" for action in case.next_actions[:3])
            }

            message = await self.send_victim_communication_async(
                victim_profile=victim_profile,
                case=case,
                communication_type=CommunicationType.STATUS_UPDATE,
                custom_variables=custom_variables,
                priority=NotificationPriority.HIGH
            )

            logger.info(f"Sent status notification for case {case.case_id}: {previous_status.value} -> {case.case_status.value}")
            return message

        except Exception as e:
            logger.error(f"Error sending status notification: {e}")
            raise CommunicationError(f"Failed to send status notification: {e}")

    def get_pending_messages_count(self) -> int:
        """Get count of pending messages."""
        return len(self.pending_messages)

    def get_sent_messages_count(self) -> int:
        """Get count of sent messages."""
        return len(self.sent_messages)

    async def cleanup_old_messages_async(self, days_old: int = 90) -> int:
        """
        Clean up old communication records.

        Args:
            days_old: Age threshold in days

        Returns:
            Number of messages cleaned up
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days_old)
            cleaned_count = 0

            # Clean up sent messages
            messages_to_remove = []
            for message in self.sent_messages:
                created_date = datetime.fromisoformat(message.created_at)
                if created_date < cutoff_date:
                    messages_to_remove.append(message)

            for message in messages_to_remove:
                self.sent_messages.remove(message)
                cleaned_count += 1

            # Clean up communication history
            for victim_id, messages in self.communication_history.items():
                messages_to_remove = []
                for message in messages:
                    created_date = datetime.fromisoformat(message.created_at)
                    if created_date < cutoff_date:
                        messages_to_remove.append(message)

                for message in messages_to_remove:
                    messages.remove(message)
                    cleaned_count += 1

            logger.info(f"Cleaned up {cleaned_count} old communication records")
            return cleaned_count

        except Exception as e:
            logger.error(f"Error cleaning up old messages: {e}")
            return 0
