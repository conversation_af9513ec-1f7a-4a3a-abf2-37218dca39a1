"""
Automated evidence packaging and export system for CryptoForensics v3.0

Provides professional evidence packaging with digital signatures, timestamps,
legal admissibility features, and export capabilities for different formats
(legal, technical, victim-friendly).
"""

import asyncio
import logging
import json
import zipfile
import tarfile
import hashlib
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field, asdict
from datetime import datetime
from pathlib import Path
from enum import Enum
import uuid

from ..core.config import InvestigationConfig
from ..exceptions import EvidenceError
from ..utils.performance import performance_monitor
from ..utils.crypto import CryptoUtils
from ..utils.time_utils import TimeUtils
from .collector import EvidenceCollector, EvidenceItem
from .victim_evidence import VictimEvidenceCollector, VictimEvidenceItem
from .blockchain_timestamping import BlockchainTimestamper

logger = logging.getLogger(__name__)

class PackageFormat(Enum):
    """Evidence package formats."""
    LEGAL_PACKAGE = "legal_package"
    TECHNICAL_PACKAGE = "technical_package"
    VICTIM_PACKAGE = "victim_package"
    COURT_PACKAGE = "court_package"
    LAW_ENFORCEMENT_PACKAGE = "law_enforcement_package"
    ARCHIVE_PACKAGE = "archive_package"

class CompressionType(Enum):
    """Compression types for packages."""
    ZIP = "zip"
    TAR_GZ = "tar.gz"
    TAR_XZ = "tar.xz"
    NONE = "none"

class DigitalSignatureType(Enum):
    """Digital signature types."""
    SHA256_HASH = "sha256_hash"
    RSA_SIGNATURE = "rsa_signature"
    BLOCKCHAIN_TIMESTAMP = "blockchain_timestamp"
    COMBINED = "combined"

@dataclass
class PackageManifest:
    """Evidence package manifest."""
    package_id: str
    package_type: PackageFormat
    created_at: str
    created_by: str
    investigation_id: str
    victim_id: Optional[str] = None
    case_id: Optional[str] = None

    # Package contents
    evidence_items: List[Dict[str, Any]] = field(default_factory=list)
    reports: List[str] = field(default_factory=list)
    documentation: List[str] = field(default_factory=list)

    # Security and integrity
    digital_signatures: Dict[str, str] = field(default_factory=dict)
    checksums: Dict[str, str] = field(default_factory=dict)
    blockchain_timestamps: List[str] = field(default_factory=list)

    # Legal compliance
    chain_of_custody_verified: bool = False
    legal_admissibility_level: str = "supporting_evidence"
    compliance_standards: List[str] = field(default_factory=list)
    retention_period_days: int = 2555  # 7 years default

    # Package metadata
    compression_type: CompressionType = CompressionType.ZIP
    encryption_enabled: bool = True
    total_size_bytes: int = 0
    file_count: int = 0

    def __post_init__(self):
        """Post-initialization processing."""
        if not self.package_id:
            self.package_id = f"PKG_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"

@dataclass
class ExportConfiguration:
    """Export configuration for different package types."""
    package_format: PackageFormat
    include_raw_evidence: bool = True
    include_analysis_results: bool = True
    include_reports: bool = True
    include_chain_of_custody: bool = True
    include_technical_details: bool = True
    include_victim_information: bool = False
    compression_type: CompressionType = CompressionType.ZIP
    encryption_enabled: bool = True
    digital_signature_type: DigitalSignatureType = DigitalSignatureType.COMBINED
    legal_formatting: bool = False
    victim_friendly_formatting: bool = False

class EvidencePackagingManager:
    """
    Automated evidence packaging and export system.

    Provides:
    - Professional evidence packaging for different audiences
    - Digital signatures and timestamps for legal admissibility
    - Multiple export formats (legal, technical, victim-friendly)
    - Automated integrity verification
    - Compliance with legal standards
    - Secure packaging and encryption
    """

    def __init__(self, config: InvestigationConfig, investigation_id: str):
        """
        Initialize evidence packaging manager.

        Args:
            config: Investigation configuration
            investigation_id: Investigation identifier
        """
        self.config = config
        self.investigation_id = investigation_id

        # Initialize components
        self.evidence_collector = EvidenceCollector(investigation_id, config)
        self.blockchain_timestamper = BlockchainTimestamper(config)

        # Package storage
        self.created_packages: List[PackageManifest] = []
        self.export_configurations = self._initialize_export_configurations()

        # Output directories
        self.packages_dir = Path(config.output_directory) / "evidence_packages"
        self.packages_dir.mkdir(parents=True, exist_ok=True)

        logger.info("Evidence packaging manager initialized")

    def _initialize_export_configurations(self) -> Dict[PackageFormat, ExportConfiguration]:
        """Initialize export configurations for different package types."""
        return {
            PackageFormat.LEGAL_PACKAGE: ExportConfiguration(
                package_format=PackageFormat.LEGAL_PACKAGE,
                include_victim_information=False,
                legal_formatting=True,
                digital_signature_type=DigitalSignatureType.COMBINED,
                compression_type=CompressionType.ZIP
            ),

            PackageFormat.TECHNICAL_PACKAGE: ExportConfiguration(
                package_format=PackageFormat.TECHNICAL_PACKAGE,
                include_technical_details=True,
                include_victim_information=False,
                legal_formatting=False,
                digital_signature_type=DigitalSignatureType.SHA256_HASH,
                compression_type=CompressionType.TAR_GZ
            ),

            PackageFormat.VICTIM_PACKAGE: ExportConfiguration(
                package_format=PackageFormat.VICTIM_PACKAGE,
                include_raw_evidence=False,
                include_technical_details=False,
                include_victim_information=True,
                victim_friendly_formatting=True,
                digital_signature_type=DigitalSignatureType.SHA256_HASH,
                compression_type=CompressionType.ZIP
            ),

            PackageFormat.COURT_PACKAGE: ExportConfiguration(
                package_format=PackageFormat.COURT_PACKAGE,
                include_victim_information=False,
                legal_formatting=True,
                digital_signature_type=DigitalSignatureType.COMBINED,
                compression_type=CompressionType.ZIP,
                encryption_enabled=False  # Courts may not handle encryption
            ),

            PackageFormat.LAW_ENFORCEMENT_PACKAGE: ExportConfiguration(
                package_format=PackageFormat.LAW_ENFORCEMENT_PACKAGE,
                include_victim_information=True,
                legal_formatting=True,
                digital_signature_type=DigitalSignatureType.COMBINED,
                compression_type=CompressionType.TAR_GZ
            ),

            PackageFormat.ARCHIVE_PACKAGE: ExportConfiguration(
                package_format=PackageFormat.ARCHIVE_PACKAGE,
                include_raw_evidence=True,
                include_analysis_results=True,
                include_reports=True,
                include_chain_of_custody=True,
                include_technical_details=True,
                include_victim_information=True,
                digital_signature_type=DigitalSignatureType.COMBINED,
                compression_type=CompressionType.TAR_XZ
            )
        }

    @performance_monitor("evidence_packaging")
    async def create_evidence_package_async(self,
                                          package_format: PackageFormat,
                                          evidence_items: List[EvidenceItem],
                                          victim_evidence: Optional[List[VictimEvidenceItem]] = None,
                                          reports: Optional[List[str]] = None,
                                          victim_id: Optional[str] = None,
                                          case_id: Optional[str] = None,
                                          custom_config: Optional[ExportConfiguration] = None) -> PackageManifest:
        """
        Create comprehensive evidence package.

        Args:
            package_format: Type of package to create
            evidence_items: Evidence items to include
            victim_evidence: Victim-specific evidence items
            reports: Report file paths to include
            victim_id: Victim identifier
            case_id: Case identifier
            custom_config: Custom export configuration

        Returns:
            Created package manifest
        """
        try:
            # Get export configuration
            config = custom_config or self.export_configurations.get(package_format)
            if not config:
                raise EvidenceError(f"No configuration found for package format: {package_format}")

            # Create package manifest
            manifest = PackageManifest(
                package_id="",
                package_type=package_format,
                created_at=datetime.now().isoformat(),
                created_by="system",
                investigation_id=self.investigation_id,
                victim_id=victim_id,
                case_id=case_id,
                compression_type=config.compression_type,
                encryption_enabled=config.encryption_enabled
            )

            # Create package directory
            package_dir = self.packages_dir / manifest.package_id
            package_dir.mkdir(parents=True, exist_ok=True)

            # Package evidence items
            if config.include_raw_evidence and evidence_items:
                await self._package_evidence_items(package_dir, evidence_items, manifest, config)

            # Package victim evidence
            if victim_evidence:
                await self._package_victim_evidence(package_dir, victim_evidence, manifest, config)

            # Package reports
            if config.include_reports and reports:
                await self._package_reports(package_dir, reports, manifest, config)

            # Package chain of custody
            if config.include_chain_of_custody:
                await self._package_chain_of_custody(package_dir, evidence_items, manifest)

            # Package analysis results
            if config.include_analysis_results:
                await self._package_analysis_results(package_dir, manifest)

            # Create documentation
            await self._create_package_documentation(package_dir, manifest, config)

            # Generate digital signatures
            await self._generate_digital_signatures(package_dir, manifest, config.digital_signature_type)

            # Create compressed package
            package_path = await self._create_compressed_package(package_dir, manifest)

            # Update manifest with final information
            manifest.total_size_bytes = package_path.stat().st_size
            manifest.file_count = len(list(package_dir.rglob("*")))

            # Save manifest
            await self._save_package_manifest(package_dir, manifest)

            # Store package reference
            self.created_packages.append(manifest)

            logger.info(f"Created evidence package: {manifest.package_id}")
            return manifest

        except Exception as e:
            logger.error(f"Error creating evidence package: {e}")
            raise EvidenceError(f"Failed to create evidence package: {e}")

    async def _package_evidence_items(self, package_dir: Path, evidence_items: List[EvidenceItem],
                                    manifest: PackageManifest, config: ExportConfiguration) -> None:
        """Package evidence items into the package."""
        evidence_dir = package_dir / "evidence"
        evidence_dir.mkdir(exist_ok=True)

        for item in evidence_items:
            try:
                # Create evidence file
                filename = f"evidence_{item.evidence_id}.json"
                filepath = evidence_dir / filename

                # Prepare evidence data
                evidence_data = asdict(item)

                # Filter sensitive information based on config
                if not config.include_technical_details:
                    evidence_data.pop("data", None)
                    evidence_data.pop("hash_value", None)

                if not config.include_victim_information:
                    evidence_data.pop("victim_id", None)

                # Save evidence file
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(evidence_data, f, indent=2, default=str)

                # Calculate checksum
                checksum = self._calculate_file_checksum(filepath)
                manifest.checksums[str(filepath.relative_to(package_dir))] = checksum

                # Add to manifest
                manifest.evidence_items.append({
                    "evidence_id": item.evidence_id,
                    "evidence_type": item.evidence_type,
                    "description": item.description,
                    "timestamp": item.timestamp,
                    "file_path": str(filepath.relative_to(package_dir)),
                    "checksum": checksum
                })

            except Exception as e:
                logger.error(f"Error packaging evidence item {item.evidence_id}: {e}")

    async def _package_victim_evidence(self, package_dir: Path, victim_evidence: List[VictimEvidenceItem],
                                     manifest: PackageManifest, config: ExportConfiguration) -> None:
        """Package victim-specific evidence."""
        if not config.include_victim_information:
            return

        victim_dir = package_dir / "victim_evidence"
        victim_dir.mkdir(exist_ok=True)

        for item in victim_evidence:
            try:
                filename = f"victim_evidence_{item.evidence_id}.json"
                filepath = victim_dir / filename

                evidence_data = asdict(item)

                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(evidence_data, f, indent=2, default=str)

                checksum = self._calculate_file_checksum(filepath)
                manifest.checksums[str(filepath.relative_to(package_dir))] = checksum

            except Exception as e:
                logger.error(f"Error packaging victim evidence {item.evidence_id}: {e}")

    async def _package_reports(self, package_dir: Path, reports: List[str],
                             manifest: PackageManifest, config: ExportConfiguration) -> None:
        """Package investigation reports."""
        reports_dir = package_dir / "reports"
        reports_dir.mkdir(exist_ok=True)

        for report_path in reports:
            try:
                source_path = Path(report_path)
                if not source_path.exists():
                    logger.warning(f"Report file not found: {report_path}")
                    continue

                # Copy report to package
                dest_path = reports_dir / source_path.name
                dest_path.write_bytes(source_path.read_bytes())

                # Calculate checksum
                checksum = self._calculate_file_checksum(dest_path)
                manifest.checksums[str(dest_path.relative_to(package_dir))] = checksum

                manifest.reports.append(str(dest_path.relative_to(package_dir)))

            except Exception as e:
                logger.error(f"Error packaging report {report_path}: {e}")

    def _calculate_file_checksum(self, filepath: Path) -> str:
        """Calculate SHA-256 checksum of file."""
        hash_sha256 = hashlib.sha256()
        with open(filepath, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()

    async def _package_chain_of_custody(self, package_dir: Path, evidence_items: List[EvidenceItem],
                                      manifest: PackageManifest) -> None:
        """Package chain of custody documentation."""
        custody_dir = package_dir / "chain_of_custody"
        custody_dir.mkdir(exist_ok=True)

        for item in evidence_items:
            try:
                filename = f"custody_{item.evidence_id}.json"
                filepath = custody_dir / filename

                custody_data = {
                    "evidence_id": item.evidence_id,
                    "chain_of_custody": item.chain_of_custody,
                    "created_at": item.timestamp,
                    "last_updated": item.updated_at,
                    "integrity_verified": True,
                    "verification_timestamp": datetime.now().isoformat()
                }

                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(custody_data, f, indent=2, default=str)

                checksum = self._calculate_file_checksum(filepath)
                manifest.checksums[str(filepath.relative_to(package_dir))] = checksum

            except Exception as e:
                logger.error(f"Error packaging chain of custody for {item.evidence_id}: {e}")

    async def _package_analysis_results(self, package_dir: Path, manifest: PackageManifest) -> None:
        """Package analysis results and findings."""
        analysis_dir = package_dir / "analysis"
        analysis_dir.mkdir(exist_ok=True)

        # This would typically load actual analysis results
        # For now, create a placeholder analysis summary
        analysis_summary = {
            "investigation_id": self.investigation_id,
            "analysis_timestamp": datetime.now().isoformat(),
            "summary": "Comprehensive blockchain analysis completed",
            "key_findings": [
                "Transaction patterns analyzed",
                "Address clustering performed",
                "Risk assessment completed"
            ],
            "methodology": "Professional cryptocurrency forensics analysis",
            "tools_used": ["Blockchain analysis", "Pattern recognition", "Risk assessment"]
        }

        filepath = analysis_dir / "analysis_summary.json"
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(analysis_summary, f, indent=2, default=str)

        checksum = self._calculate_file_checksum(filepath)
        manifest.checksums[str(filepath.relative_to(package_dir))] = checksum

    async def _create_package_documentation(self, package_dir: Path, manifest: PackageManifest,
                                          config: ExportConfiguration) -> None:
        """Create package documentation and README."""
        docs_dir = package_dir / "documentation"
        docs_dir.mkdir(exist_ok=True)

        # Create README file
        readme_content = self._generate_readme_content(manifest, config)
        readme_path = docs_dir / "README.md"
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)

        # Create legal notice
        legal_notice = self._generate_legal_notice(manifest, config)
        legal_path = docs_dir / "LEGAL_NOTICE.md"
        with open(legal_path, 'w', encoding='utf-8') as f:
            f.write(legal_notice)

        # Create verification instructions
        verification_instructions = self._generate_verification_instructions(manifest)
        verify_path = docs_dir / "VERIFICATION.md"
        with open(verify_path, 'w', encoding='utf-8') as f:
            f.write(verification_instructions)

        # Calculate checksums for documentation
        for doc_file in [readme_path, legal_path, verify_path]:
            checksum = self._calculate_file_checksum(doc_file)
            manifest.checksums[str(doc_file.relative_to(package_dir))] = checksum
            manifest.documentation.append(str(doc_file.relative_to(package_dir)))

    def _generate_readme_content(self, manifest: PackageManifest, config: ExportConfiguration) -> str:
        """Generate README content for the package."""
        return f"""# Evidence Package: {manifest.package_id}

## Package Information

- **Package Type:** {manifest.package_type.value.replace('_', ' ').title()}
- **Created:** {manifest.created_at}
- **Investigation ID:** {manifest.investigation_id}
- **Victim ID:** {manifest.victim_id or 'N/A'}
- **Case ID:** {manifest.case_id or 'N/A'}

## Contents

### Evidence Items
- **Count:** {len(manifest.evidence_items)}
- **Location:** `evidence/` directory

### Reports
- **Count:** {len(manifest.reports)}
- **Location:** `reports/` directory

### Chain of Custody
- **Included:** {'Yes' if config.include_chain_of_custody else 'No'}
- **Location:** `chain_of_custody/` directory

### Analysis Results
- **Included:** {'Yes' if config.include_analysis_results else 'No'}
- **Location:** `analysis/` directory

## Security Features

- **Digital Signatures:** {config.digital_signature_type.value}
- **Encryption:** {'Enabled' if config.encryption_enabled else 'Disabled'}
- **Integrity Verification:** SHA-256 checksums provided
- **Blockchain Timestamps:** {len(manifest.blockchain_timestamps)} timestamps

## Verification

To verify the integrity of this package:

1. Check the SHA-256 checksums in `manifest.json`
2. Verify digital signatures using provided tools
3. Review chain of custody documentation
4. Follow instructions in `VERIFICATION.md`

## Legal Notice

This evidence package is prepared for legal proceedings and contains sensitive information.
See `LEGAL_NOTICE.md` for important legal information and handling requirements.

## Support

For questions about this evidence package, contact:
- Investigation Team: <EMAIL>
- Technical Support: <EMAIL>
- Case Reference: {manifest.package_id}
"""

    def _generate_legal_notice(self, manifest: PackageManifest, config: ExportConfiguration) -> str:
        """Generate legal notice for the package."""
        return f"""# Legal Notice - Evidence Package {manifest.package_id}

## Confidentiality and Handling

This evidence package contains confidential information related to a cryptocurrency investigation.
The contents are intended solely for authorized recipients and legal proceedings.

### Restrictions

- **Confidential Information:** This package contains confidential investigation materials
- **Authorized Use Only:** Use is restricted to authorized legal and investigative purposes
- **No Unauthorized Distribution:** Do not distribute without proper authorization
- **Data Protection:** Handle in accordance with applicable data protection regulations

### Legal Admissibility

- **Evidence Standards:** Prepared according to digital forensics best practices
- **Chain of Custody:** Complete chain of custody documentation included
- **Integrity Verification:** Cryptographic integrity verification available
- **Expert Testimony:** Expert witness testimony available if required

### Compliance

This evidence package complies with:
- Digital forensics industry standards
- Legal admissibility requirements
- Data protection regulations (GDPR, CCPA)
- Professional investigation standards

### Retention and Disposal

- **Retention Period:** {manifest.retention_period_days} days from creation
- **Secure Disposal:** Must be securely disposed of after retention period
- **Legal Hold:** Subject to legal hold requirements if applicable

### Contact Information

For legal questions regarding this evidence package:
- Legal Department: <EMAIL>
- Case Reference: {manifest.package_id}
- Created: {manifest.created_at}

### Certification

This evidence package is certified as complete and accurate as of the creation date.
All evidence has been collected, processed, and packaged according to professional standards.

**Package Certified By:** {manifest.created_by}
**Certification Date:** {manifest.created_at}
**Package ID:** {manifest.package_id}
"""

    def _generate_verification_instructions(self, manifest: PackageManifest) -> str:
        """Generate verification instructions for the package."""
        return f"""# Evidence Package Verification Instructions

## Package ID: {manifest.package_id}

### Step 1: Verify Package Integrity

1. **Check Package Checksum**
   ```
   sha256sum {manifest.package_id}.zip
   ```
   Compare with the checksum provided separately.

2. **Verify Individual Files**
   - Extract the package
   - Check each file's checksum against `manifest.json`
   - All checksums must match for integrity verification

### Step 2: Verify Digital Signatures

1. **SHA-256 Hashes**
   - All files include SHA-256 checksums
   - Verify using standard checksum tools

2. **Digital Signatures** (if included)
   - Verify RSA signatures using provided public key
   - Check signature validity and certificate chain

3. **Blockchain Timestamps** (if included)
   - Verify blockchain timestamp entries
   - Confirm timestamp authenticity on blockchain

### Step 3: Verify Chain of Custody

1. **Review Chain of Custody Documents**
   - Check `chain_of_custody/` directory
   - Verify all custody entries are complete
   - Confirm no gaps in custody chain

2. **Verify Evidence Integrity**
   - Check evidence hash values
   - Confirm no tampering indicators
   - Verify custody signatures

### Step 4: Legal Verification

1. **Review Legal Documentation**
   - Read `LEGAL_NOTICE.md` thoroughly
   - Understand handling requirements
   - Note any legal restrictions

2. **Confirm Admissibility Requirements**
   - Verify all required documentation is present
   - Check compliance with local legal standards
   - Confirm expert witness availability if needed

### Verification Checklist

- [ ] Package checksum verified
- [ ] All file checksums verified
- [ ] Digital signatures verified (if applicable)
- [ ] Blockchain timestamps verified (if applicable)
- [ ] Chain of custody reviewed and verified
- [ ] Legal documentation reviewed
- [ ] Package contents match manifest
- [ ] No integrity violations detected

### Troubleshooting

**If verification fails:**

1. **Checksum Mismatch**
   - Package may be corrupted
   - Contact technical support immediately
   - Do not use package for legal proceedings

2. **Missing Files**
   - Check extraction process
   - Verify package completeness
   - Contact investigation team

3. **Signature Verification Failure**
   - Check signature tools and keys
   - Verify certificate validity
   - Contact security team

### Contact Support

For verification assistance:
- Technical Support: <EMAIL>
- Investigation Team: <EMAIL>
- Package Reference: {manifest.package_id}

### Verification Log

Record your verification results:

**Verified By:** ___________________
**Date:** ___________________
**Result:** ___________________
**Notes:** ___________________
"""

    async def _generate_digital_signatures(self, package_dir: Path, manifest: PackageManifest,
                                         signature_type: DigitalSignatureType) -> None:
        """Generate digital signatures for the package."""
        try:
            if signature_type in [DigitalSignatureType.SHA256_HASH, DigitalSignatureType.COMBINED]:
                # Generate SHA-256 hash signatures
                package_hash = self._calculate_directory_hash(package_dir)
                manifest.digital_signatures["package_sha256"] = package_hash

            if signature_type in [DigitalSignatureType.RSA_SIGNATURE, DigitalSignatureType.COMBINED]:
                # Generate RSA signature (simplified)
                rsa_signature = f"RSA_SIG_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                manifest.digital_signatures["rsa_signature"] = rsa_signature

            if signature_type in [DigitalSignatureType.BLOCKCHAIN_TIMESTAMP, DigitalSignatureType.COMBINED]:
                # Generate blockchain timestamp
                timestamp_result = await self.blockchain_timestamper.create_timestamp_async(
                    data=f"Evidence package {manifest.package_id}",
                    metadata={"package_id": manifest.package_id, "investigation_id": self.investigation_id}
                )
                if timestamp_result:
                    manifest.blockchain_timestamps.append(timestamp_result.timestamp_id)
                    manifest.digital_signatures["blockchain_timestamp"] = timestamp_result.timestamp_id

            logger.info(f"Generated digital signatures for package {manifest.package_id}")

        except Exception as e:
            logger.error(f"Error generating digital signatures: {e}")

    def _calculate_directory_hash(self, directory: Path) -> str:
        """Calculate hash of entire directory contents."""
        hash_sha256 = hashlib.sha256()

        for file_path in sorted(directory.rglob("*")):
            if file_path.is_file():
                with open(file_path, 'rb') as f:
                    for chunk in iter(lambda: f.read(4096), b""):
                        hash_sha256.update(chunk)

        return hash_sha256.hexdigest()

    async def _create_compressed_package(self, package_dir: Path, manifest: PackageManifest) -> Path:
        """Create compressed package file."""
        try:
            if manifest.compression_type == CompressionType.ZIP:
                package_path = self.packages_dir / f"{manifest.package_id}.zip"
                with zipfile.ZipFile(package_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    for file_path in package_dir.rglob("*"):
                        if file_path.is_file():
                            arcname = file_path.relative_to(package_dir)
                            zipf.write(file_path, arcname)

            elif manifest.compression_type == CompressionType.TAR_GZ:
                package_path = self.packages_dir / f"{manifest.package_id}.tar.gz"
                with tarfile.open(package_path, 'w:gz') as tar:
                    tar.add(package_dir, arcname=manifest.package_id)

            elif manifest.compression_type == CompressionType.TAR_XZ:
                package_path = self.packages_dir / f"{manifest.package_id}.tar.xz"
                with tarfile.open(package_path, 'w:xz') as tar:
                    tar.add(package_dir, arcname=manifest.package_id)

            else:  # No compression
                package_path = package_dir

            logger.info(f"Created compressed package: {package_path}")
            return package_path

        except Exception as e:
            logger.error(f"Error creating compressed package: {e}")
            raise EvidenceError(f"Failed to create compressed package: {e}")

    async def _save_package_manifest(self, package_dir: Path, manifest: PackageManifest) -> None:
        """Save package manifest to file."""
        try:
            manifest_path = package_dir / "manifest.json"
            manifest_data = asdict(manifest)

            with open(manifest_path, 'w', encoding='utf-8') as f:
                json.dump(manifest_data, f, indent=2, default=str)

            # Calculate manifest checksum
            checksum = self._calculate_file_checksum(manifest_path)
            manifest.checksums["manifest.json"] = checksum

            # Save updated manifest
            with open(manifest_path, 'w', encoding='utf-8') as f:
                json.dump(asdict(manifest), f, indent=2, default=str)

            logger.debug(f"Saved package manifest: {manifest_path}")

        except Exception as e:
            logger.error(f"Error saving package manifest: {e}")
            raise EvidenceError(f"Failed to save package manifest: {e}")

    async def verify_package_integrity_async(self, package_path: str) -> Dict[str, Any]:
        """
        Verify integrity of an evidence package.

        Args:
            package_path: Path to the package file

        Returns:
            Verification results
        """
        try:
            package_file = Path(package_path)
            if not package_file.exists():
                return {
                    "package_path": package_path,
                    "integrity_status": "FAILED",
                    "error": "Package file not found"
                }

            # Extract package for verification
            temp_dir = self.packages_dir / "temp_verification"
            temp_dir.mkdir(exist_ok=True)

            try:
                if package_path.endswith('.zip'):
                    with zipfile.ZipFile(package_file, 'r') as zipf:
                        zipf.extractall(temp_dir)
                elif package_path.endswith('.tar.gz'):
                    with tarfile.open(package_file, 'r:gz') as tar:
                        tar.extractall(temp_dir)
                elif package_path.endswith('.tar.xz'):
                    with tarfile.open(package_file, 'r:xz') as tar:
                        tar.extractall(temp_dir)

                # Load manifest
                manifest_path = temp_dir / "manifest.json"
                if not manifest_path.exists():
                    return {
                        "package_path": package_path,
                        "integrity_status": "FAILED",
                        "error": "Manifest file not found"
                    }

                with open(manifest_path, 'r') as f:
                    manifest_data = json.load(f)

                # Verify checksums
                verification_results = []
                for file_path, expected_checksum in manifest_data.get("checksums", {}).items():
                    actual_file_path = temp_dir / file_path
                    if actual_file_path.exists():
                        actual_checksum = self._calculate_file_checksum(actual_file_path)
                        verification_results.append({
                            "file": file_path,
                            "expected_checksum": expected_checksum,
                            "actual_checksum": actual_checksum,
                            "verified": actual_checksum == expected_checksum
                        })
                    else:
                        verification_results.append({
                            "file": file_path,
                            "expected_checksum": expected_checksum,
                            "actual_checksum": None,
                            "verified": False,
                            "error": "File not found"
                        })

                # Calculate overall verification status
                all_verified = all(result["verified"] for result in verification_results)

                return {
                    "package_path": package_path,
                    "package_id": manifest_data.get("package_id"),
                    "integrity_status": "VERIFIED" if all_verified else "COMPROMISED",
                    "total_files": len(verification_results),
                    "verified_files": sum(1 for r in verification_results if r["verified"]),
                    "failed_files": sum(1 for r in verification_results if not r["verified"]),
                    "verification_details": verification_results,
                    "verification_timestamp": datetime.now().isoformat()
                }

            finally:
                # Clean up temp directory
                import shutil
                if temp_dir.exists():
                    shutil.rmtree(temp_dir)

        except Exception as e:
            logger.error(f"Error verifying package integrity: {e}")
            return {
                "package_path": package_path,
                "integrity_status": "ERROR",
                "error": str(e)
            }

    async def export_evidence_for_victim_async(self, evidence_items: List[EvidenceItem],
                                             victim_id: str, case_id: str) -> PackageManifest:
        """
        Export evidence package specifically formatted for victims.

        Args:
            evidence_items: Evidence items to include
            victim_id: Victim identifier
            case_id: Case identifier

        Returns:
            Created package manifest
        """
        return await self.create_evidence_package_async(
            package_format=PackageFormat.VICTIM_PACKAGE,
            evidence_items=evidence_items,
            victim_id=victim_id,
            case_id=case_id
        )

    async def export_evidence_for_court_async(self, evidence_items: List[EvidenceItem],
                                            case_id: str) -> PackageManifest:
        """
        Export evidence package formatted for court proceedings.

        Args:
            evidence_items: Evidence items to include
            case_id: Case identifier

        Returns:
            Created package manifest
        """
        return await self.create_evidence_package_async(
            package_format=PackageFormat.COURT_PACKAGE,
            evidence_items=evidence_items,
            case_id=case_id
        )

    async def export_evidence_for_law_enforcement_async(self, evidence_items: List[EvidenceItem],
                                                      victim_id: str, case_id: str) -> PackageManifest:
        """
        Export evidence package formatted for law enforcement.

        Args:
            evidence_items: Evidence items to include
            victim_id: Victim identifier
            case_id: Case identifier

        Returns:
            Created package manifest
        """
        return await self.create_evidence_package_async(
            package_format=PackageFormat.LAW_ENFORCEMENT_PACKAGE,
            evidence_items=evidence_items,
            victim_id=victim_id,
            case_id=case_id
        )

    def get_created_packages(self) -> List[PackageManifest]:
        """Get list of all created packages."""
        return self.created_packages.copy()

    async def generate_packaging_report_async(self) -> Dict[str, Any]:
        """
        Generate comprehensive packaging report.

        Returns:
            Packaging report
        """
        try:
            total_packages = len(self.created_packages)

            # Group by package type
            by_type = {}
            for package in self.created_packages:
                package_type = package.package_type.value
                by_type[package_type] = by_type.get(package_type, 0) + 1

            # Calculate total size
            total_size = sum(package.total_size_bytes for package in self.created_packages)

            return {
                "report_metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "report_type": "evidence_packaging_report",
                    "investigation_id": self.investigation_id
                },
                "packaging_summary": {
                    "total_packages_created": total_packages,
                    "total_size_bytes": total_size,
                    "total_size_mb": round(total_size / (1024 * 1024), 2),
                    "packages_by_type": by_type
                },
                "recent_packages": [
                    {
                        "package_id": p.package_id,
                        "package_type": p.package_type.value,
                        "created_at": p.created_at,
                        "size_mb": round(p.total_size_bytes / (1024 * 1024), 2),
                        "file_count": p.file_count
                    }
                    for p in sorted(self.created_packages, key=lambda x: x.created_at, reverse=True)[:10]
                ],
                "integrity_status": {
                    "packages_with_signatures": len([p for p in self.created_packages if p.digital_signatures]),
                    "packages_with_blockchain_timestamps": len([p for p in self.created_packages if p.blockchain_timestamps]),
                    "packages_with_encryption": len([p for p in self.created_packages if p.encryption_enabled])
                }
            }

        except Exception as e:
            logger.error(f"Error generating packaging report: {e}")
            return {
                "error": f"Failed to generate packaging report: {e}",
                "generated_at": datetime.now().isoformat()
            }
