"""
Exception classes for CryptoForensics

Defines all custom exception classes used throughout the package.
"""

class CryptoForensicsError(Exception):
    """Base exception for CryptoForensics package."""
    pass

class ValidationError(CryptoForensicsError):
    """Raised when input validation fails."""
    pass

class APIError(CryptoForensicsError):
    """Raised when API operations fail."""
    pass

class EvidenceError(CryptoForensicsError):
    """Raised when evidence collection or integrity issues occur."""
    pass

class AnalysisError(CryptoForensicsError):
    """Raised when analysis operations fail."""
    pass

class VisualizationError(CryptoForensicsError):
    """Raised when visualization operations fail."""
    pass

class SecurityError(CryptoForensicsError):
    """Raised when security operations fail."""
    pass

class ConfigurationError(CryptoForensicsError):
    """Raised when configuration issues occur."""
    pass

class InvestigationError(CryptoForensicsError):
    """Raised when investigation operations fail."""
    pass

class ReportingError(CryptoForensicsError):
    """Raised when reporting operations fail."""
    pass

class CommunicationError(CryptoForensicsError):
    """Raised when communication operations fail."""
    pass
